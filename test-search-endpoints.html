<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار endpoints البحث</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>اختبار endpoints البحث الجديدة</h1>
    
    <div class="test-section">
        <h3>اختبار البحث في الإجازات</h3>
        <button onclick="testVacationsSearch()">اختبار البحث في الإجازات</button>
        <button onclick="testVacationsAll()">اختبار جلب جميع الإجازات</button>
        <div id="vacations-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>اختبار البحث في الساعات الإضافية</h3>
        <button onclick="testExtraHoursSearch()">اختبار البحث في الساعات الإضافية</button>
        <button onclick="testExtraHoursAll()">اختبار جلب جميع الساعات الإضافية</button>
        <div id="extra-hours-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>اختبار البحث في الموظفين</h3>
        <button onclick="testEmployeesSearch()">اختبار البحث في الموظفين</button>
        <button onclick="testEmployeesAll()">اختبار جلب جميع الموظفين</button>
        <div id="employees-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>اختبار البحث في التقييمات</h3>
        <button onclick="testEvaluationsSearch()">اختبار البحث في التقييمات</button>
        <button onclick="testEvaluationsAll()">اختبار جلب جميع التقييمات</button>
        <div id="evaluations-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>اختبار البحث في التدريب</h3>
        <button onclick="testTrainingSearch()">اختبار البحث في التدريب</button>
        <button onclick="testTrainingAll()">اختبار جلب جميع الدورات التدريبية</button>
        <div id="training-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>اختبار البحث في السلف</h3>
        <button onclick="testSalaryAdvancesSearch()">اختبار البحث في السلف</button>
        <button onclick="testSalaryAdvancesAll()">اختبار جلب جميع السلف</button>
        <div id="salary-advances-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>اختبار البحث في العهد</h3>
        <button onclick="testCustodySearch()">اختبار البحث في العهد</button>
        <button onclick="testCustodyDeliverySearch()">اختبار البحث في سجلات التسليم</button>
        <div id="custody-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>اختبار البحث في الموظفين المثاليين</h3>
        <button onclick="testIdealEmployeesSearch()">اختبار البحث في الموظفين المثاليين</button>
        <button onclick="testIdealEmployeesAll()">اختبار جلب جميع الموظفين المثاليين</button>
        <div id="ideal-employees-result" class="result"></div>
    </div>

    <script>
        // دالة مساعدة لعرض النتائج
        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        // اختبار البحث في الإجازات
        async function testVacationsSearch() {
            try {
                const response = await fetch('/api/vacations/search?employee_name=أحمد', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displayResult('vacations-result', {
                        status: 'نجح',
                        message: 'تم البحث في الإجازات بنجاح',
                        count: data.length,
                        data: data.slice(0, 3) // عرض أول 3 نتائج فقط
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('vacations-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار جلب جميع الإجازات
        async function testVacationsAll() {
            try {
                const response = await fetch('/api/vacations', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displayResult('vacations-result', {
                        status: 'نجح',
                        message: 'تم جلب جميع الإجازات بنجاح',
                        count: data.length,
                        data: data.slice(0, 3) // عرض أول 3 نتائج فقط
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('vacations-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار البحث في الساعات الإضافية
        async function testExtraHoursSearch() {
            try {
                const response = await fetch('/api/extra-hours/search?employee_name=أحمد', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    displayResult('extra-hours-result', {
                        status: 'نجح',
                        message: 'تم البحث في الساعات الإضافية بنجاح',
                        count: data.length,
                        data: data.slice(0, 3) // عرض أول 3 نتائج فقط
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('extra-hours-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار جلب جميع الساعات الإضافية
        async function testExtraHoursAll() {
            try {
                const response = await fetch('/api/extra-hours', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('extra-hours-result', {
                        status: 'نجح',
                        message: 'تم جلب جميع الساعات الإضافية بنجاح',
                        count: data.length,
                        data: data.slice(0, 3) // عرض أول 3 نتائج فقط
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('extra-hours-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار البحث في الموظفين
        async function testEmployeesSearch() {
            try {
                const response = await fetch('/api/employees?search=أحمد', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('employees-result', {
                        status: 'نجح',
                        message: 'تم البحث في الموظفين بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('employees-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار جلب جميع الموظفين
        async function testEmployeesAll() {
            try {
                const response = await fetch('/api/employees', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('employees-result', {
                        status: 'نجح',
                        message: 'تم جلب جميع الموظفين بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('employees-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار البحث في التقييمات
        async function testEvaluationsSearch() {
            try {
                const response = await fetch('/api/evaluations/search?employee_name=أحمد', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('evaluations-result', {
                        status: 'نجح',
                        message: 'تم البحث في التقييمات بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('evaluations-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار جلب جميع التقييمات
        async function testEvaluationsAll() {
            try {
                const response = await fetch('/api/evaluations', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('evaluations-result', {
                        status: 'نجح',
                        message: 'تم جلب جميع التقييمات بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('evaluations-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار البحث في التدريب
        async function testTrainingSearch() {
            try {
                const response = await fetch('/api/training/search?employee_name=أحمد', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('training-result', {
                        status: 'نجح',
                        message: 'تم البحث في التدريب بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('training-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار جلب جميع الدورات التدريبية
        async function testTrainingAll() {
            try {
                const response = await fetch('/api/training', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('training-result', {
                        status: 'نجح',
                        message: 'تم جلب جميع الدورات التدريبية بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('training-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار البحث في السلف
        async function testSalaryAdvancesSearch() {
            try {
                const response = await fetch('/api/salary-advances/search?employee_name=أحمد', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('salary-advances-result', {
                        status: 'نجح',
                        message: 'تم البحث في السلف بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('salary-advances-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار جلب جميع السلف
        async function testSalaryAdvancesAll() {
            try {
                const response = await fetch('/api/salary-advances', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('salary-advances-result', {
                        status: 'نجح',
                        message: 'تم جلب جميع السلف بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('salary-advances-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار البحث في العهد
        async function testCustodySearch() {
            try {
                const response = await fetch('/api/custody/search?name=كمبيوتر', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('custody-result', {
                        status: 'نجح',
                        message: 'تم البحث في العهد بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('custody-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار البحث في سجلات التسليم
        async function testCustodyDeliverySearch() {
            try {
                const response = await fetch('/api/custody/delivery/search?employee_name=أحمد', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('custody-result', {
                        status: 'نجح',
                        message: 'تم البحث في سجلات التسليم بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('custody-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار البحث في الموظفين المثاليين
        async function testIdealEmployeesSearch() {
            try {
                const response = await fetch('/api/ideal-employees/search?employee_name=أحمد', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('ideal-employees-result', {
                        status: 'نجح',
                        message: 'تم البحث في الموظفين المثاليين بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('ideal-employees-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }

        // اختبار جلب جميع الموظفين المثاليين
        async function testIdealEmployeesAll() {
            try {
                const response = await fetch('/api/ideal-employees', {
                    headers: {
                        'Authorization': 'Bearer test-token'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayResult('ideal-employees-result', {
                        status: 'نجح',
                        message: 'تم جلب جميع الموظفين المثاليين بنجاح',
                        count: data.length,
                        data: data.slice(0, 3)
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                displayResult('ideal-employees-result', {
                    status: 'فشل',
                    error: error.message
                }, true);
            }
        }
    </script>
</body>
</html>
