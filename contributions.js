// متغيرات عامة - استخدام config.js والحالة العامة
const API_URL = window.CONFIG ? window.CONFIG.API_URL : (localStorage.getItem('serverUrl') || 'http://localhost:5500/api');
let employees = window.GlobalState ? window.GlobalState.employees : [];
let contributions = window.GlobalState ? window.GlobalState.contributions : [];

// طباعة معلومات التصحيح
console.log('API_URL المستخدم:', API_URL);
console.log('window.CONFIG:', window.CONFIG);
console.log('localStorage serverUrl:', localStorage.getItem('serverUrl'));

// عناصر DOM
document.addEventListener('DOMContentLoaded', function() {
  // التحقق من المحتوى المحدد من البطاقات
  checkSelectedContent();

  // تحميل البيانات
  initializePage();
  
  // إعداد مستمعي الأحداث
  setupEventListeners();
});

// تهيئة الصفحة
async function initializePage() {
  try {
    await loadEmployees();
    await loadContributions();
    displayContributions();
    

  } catch (error) {
    console.error('خطأ في تهيئة الصفحة:', error);
    showNotification('خطأ في تحميل البيانات', 'error');
  }
}

// تحميل الموظفين
async function loadEmployees() {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/employees`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      employees = await response.json();
      console.log('تم تحميل الموظفين:', employees.length);
    } else {
      console.error(`فشل في تحميل الموظفين: ${response.status} ${response.statusText}`);
      throw new Error(`فشل في تحميل الموظفين: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('خطأ في تحميل الموظفين:', error);
    employees = [];
    throw error;
  }
}

// تحميل المساهمات
async function loadContributions() {
  try {
    // محاولة إنشاء جدول المساهمات أولاً إذا لم يكن موجودًا
    try {
      const token = localStorage.getItem('token');
      const createTableResponse = await fetch(`${API_URL}/contributions/create-contributions-table`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (createTableResponse.ok) {
        console.log('تم التحقق من جدول المساهمات');
      } else {
        console.warn('لم يتم التحقق من جدول المساهمات:', createTableResponse.status);
      }
    } catch (createError) {
      console.warn('خطأ في التحقق من جدول المساهمات:', createError);
    }

    // محاولة تحميل المساهمات
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/contributions`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      contributions = await response.json();
      console.log('تم تحميل المساهمات:', contributions.length);
      console.log('بيانات المساهمات:', contributions); // إضافة طباعة بيانات المساهمات
    } else {
      console.error(`فشل في تحميل المساهمات: ${response.status} ${response.statusText}`);
      // إذا كان الخطأ 404، فهذا يعني أن الجدول غير موجود بعد
      if (response.status === 404) {
        contributions = [];
        console.log('جدول المساهمات غير موجود، سيتم إنشاؤه عند إضافة أول مساهمة');
      } else {
        throw new Error(`فشل في تحميل المساهمات: ${response.status} ${response.statusText}`);
      }
    }
  } catch (error) {
    console.error('خطأ في تحميل المساهمات:', error);
    contributions = [];
  }
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
  // البحث عن الموظف بالاسم أو الكود
  const employeeSearchInput = document.getElementById('employeeSearch');
  if (employeeSearchInput) {
    employeeSearchInput.addEventListener('input', handleEmployeeSearch);
    employeeSearchInput.addEventListener('change', handleEmployeeSelection);
  }
  
  // تصدير تفاصيل التقرير إلى Excel
  const exportDetailsBtn = document.getElementById('exportDetailsBtn');
  if (exportDetailsBtn) {
    exportDetailsBtn.addEventListener('click', function() {
      exportToExcel('detailsTable', 'تفاصيل_المساهمات');
    });
  }
  
  // لم نعد نحتاج إلى هذه المستمعات لأن الحقول أصبحت للقراءة فقط
  // const employeeCodeInput = document.getElementById('employeeCode');
  // if (employeeCodeInput) {
  //   employeeCodeInput.addEventListener('input', handleEmployeeCodeSearch);
  // }
  
  // const employeeNameInput = document.getElementById('employeeName');
  // if (employeeNameInput) {
  //   employeeNameInput.addEventListener('input', handleEmployeeNameSearch);
  //   employeeNameInput.addEventListener('change', handleEmployeeNameSelection);
  //   employeeNameInput.addEventListener('blur', handleEmployeeNameSelection);
  //   employeeNameInput.addEventListener('input', function() {
  //     setTimeout(handleEmployeeNameSelection, 100);
  //   });
  // }
  
  // حفظ المساهمة
  const saveContributionBtn = document.getElementById('saveContribution');
  if (saveContributionBtn) {
    saveContributionBtn.addEventListener('click', saveContribution);
  }
  
  // إعادة تعيين النموذج
  const resetFormBtn = document.getElementById('resetForm');
  if (resetFormBtn) {
    resetFormBtn.addEventListener('click', resetContributionForm);
  }
  

  
  // تصدير المساهمات إلى Excel
  const exportContributionsBtn = document.getElementById('exportContributionsBtn');
  if (exportContributionsBtn) {
    exportContributionsBtn.addEventListener('click', function() {
      exportToExcel('contribution-table', 'المساهمات');
    });
  }
  
  // البحث في عرض المساهمات
  const searchContributionsBtn = document.getElementById('searchContributionsBtn');
  if (searchContributionsBtn) {
    searchContributionsBtn.addEventListener('click', searchContributionsFromList);
  }

  // إعادة تعيين البحث
  const clearContributionSearchBtn = document.getElementById('clearContributionSearchBtn');
  if (clearContributionSearchBtn) {
    clearContributionSearchBtn.addEventListener('click', clearContributionSearch);
  }
  
  // تصدير نتائج البحث إلى Excel
  const exportFilteredContributionsBtn = document.getElementById('exportFilteredContributionsBtn');
  if (exportFilteredContributionsBtn) {
    exportFilteredContributionsBtn.addEventListener('click', function() {
      exportToExcel('filteredContributionsTable', 'نتائج_بحث_المساهمات');
    });
  }
  

  
  // البحث باسم الموظف في عرض المساهمات
  const employeeSearchView = document.getElementById('employeeSearchView');
  if (employeeSearchView) {
    // استخدام تقنية debounce لتأخير البحث حتى يتوقف المستخدم عن الكتابة
    let searchTimeout;
    employeeSearchView.addEventListener('input', function() {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        searchContributions();
      }, 500); // تأخير 500 مللي ثانية
    });
  }
  
  // البحث المباشر عن الموظف في قسم عرض المساهمات
  const searchEmployeeName = document.getElementById('searchEmployeeName');
  if (searchEmployeeName) {
    // إنشاء عنصر datalist إذا لم يكن موجودًا
    let datalist = document.getElementById('employeeSearchViewSuggestions');
    if (!datalist) {
      datalist = document.createElement('datalist');
      datalist.id = 'employeeSearchViewSuggestions';
      document.body.appendChild(datalist);
      searchEmployeeName.setAttribute('list', 'employeeSearchViewSuggestions');
    }
    
    // استخدام تقنية debounce لتأخير البحث حتى يتوقف المستخدم عن الكتابة
    let searchTimeout;
    searchEmployeeName.addEventListener('input', function() {
      const searchInput = this.value.trim();
      
      // مسح الاقتراحات عند عدم وجود نص للبحث
      if (searchInput === '') {
        datalist.innerHTML = '';
        return;
      }
      
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(async () => {
        try {
          // بناء عنوان URL للبحث - استخدام search parameter للكود والاسم
          const searchUrl = `${API_URL}/employees?search=${encodeURIComponent(searchInput)}`;

          const token = localStorage.getItem('token');
          const response = await fetch(searchUrl, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          if (response.ok) {
            const searchResults = await response.json();
            
            // عرض الاقتراحات
            datalist.innerHTML = '';
            searchResults.forEach(emp => {
              const option = document.createElement('option');
              option.value = emp.full_name;
              option.setAttribute('data-code', emp.code);
              option.setAttribute('data-name', emp.full_name);
              datalist.appendChild(option);
            });
          }
        } catch (error) {
          console.error('خطأ في البحث عن الموظفين:', error);
        }
      }, 300); // تأخير 300 مللي ثانية
    });
  }
  
  // مستمعي أحداث تقارير المساهمات
  const generateReportBtn = document.getElementById('generateReportBtn');
  if (generateReportBtn) {
    generateReportBtn.addEventListener('click', generateContributionReport);
  }
  
  const viewCompanyDetailsBtn = document.getElementById('viewCompanyDetailsBtn');
  if (viewCompanyDetailsBtn) {
    viewCompanyDetailsBtn.addEventListener('click', function() {
      showContributionDetails('company');
    });
  }
  
  const viewFundDetailsBtn = document.getElementById('viewFundDetailsBtn');
  if (viewFundDetailsBtn) {
    viewFundDetailsBtn.addEventListener('click', function() {
      showContributionDetails('fund');
    });
  }
  
  const viewTotalDetailsBtn = document.getElementById('viewTotalDetailsBtn');
  if (viewTotalDetailsBtn) {
    viewTotalDetailsBtn.addEventListener('click', function() {
      showContributionDetails('total');
    });
  }
}

// التحقق من المحتوى المحدد من البطاقات
function checkSelectedContent() {
  const selectedContent = localStorage.getItem('selectedContributionsTab');

  if (selectedContent) {
    // حذف المحتوى المحفوظ
    localStorage.removeItem('selectedContributionsTab');

    // عرض المحتوى المناسب
    showContent(selectedContent);
  } else {
    // عرض المحتوى الافتراضي (إضافة مساهمة)
    showContent('add-contribution');
  }
}

// عرض المحتوى المحدد
function showContent(contentType) {
  console.log('عرض المحتوى:', contentType);

  // إخفاء جميع المحتويات
  const allContents = document.querySelectorAll('.tab-content');
  allContents.forEach(content => {
    content.classList.remove('active');
    content.style.display = 'none';
  });

  // عرض المحتوى المحدد
  const targetContent = document.getElementById(contentType);
  if (targetContent) {
    targetContent.classList.add('active');
    targetContent.style.display = 'block';

    // تحديث عنوان الصفحة
    const pageTitle = document.querySelector('h1');
    if (pageTitle) {
      if (contentType === 'add-contribution') {
        pageTitle.textContent = 'إضافة مساهمة';
      } else if (contentType === 'view-contributions') {
        pageTitle.textContent = 'عرض المساهمات';
      } else if (contentType === 'contribution-reports') {
        pageTitle.textContent = 'تقارير المساهمات';
      }
    }

    // تحديث البيانات حسب نوع المحتوى
    if (contentType === 'view-contributions') {
      loadContributionsForView();
    } else if (contentType === 'contribution-reports') {
      initializeReportsTab();
    }
  } else {
    console.error('لم يتم العثور على المحتوى:', contentType);
  }
}

// البحث عن الموظف بالاسم أو الكود
async function handleEmployeeSearch() {
  const searchInput = document.getElementById('employeeSearch').value.trim();
  const employeeCodeInput = document.getElementById('employeeCode');
  const employeeNameInput = document.getElementById('employeeName');
  const datalist = document.getElementById('employeeSearchSuggestions');
  
  // مسح الاقتراحات والحقول عند عدم وجود نص للبحث
  if (searchInput === '') {
    employeeCodeInput.value = '';
    employeeNameInput.value = '';
    datalist.innerHTML = '';
    return;
  }
  
  // تحديد ما إذا كان البحث بالكود أو بالاسم
  const isSearchByCode = /^\d+$/.test(searchInput);
  
  try {
    // بناء عنوان URL للبحث - استخدام search parameter للكود والاسم
    const searchUrl = `${API_URL}/employees?search=${encodeURIComponent(searchInput)}`;

    // البحث في قاعدة البيانات
    const token = localStorage.getItem('token');
    const response = await fetch(searchUrl, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      const searchResults = await response.json();
      
      // تحديث الاقتراحات بنتائج البحث
      datalist.innerHTML = '';
      
      searchResults.forEach(employee => {
        const option = document.createElement('option');
        // عرض الاسم فقط في الاقتراح
        option.value = employee.full_name;
        option.setAttribute('data-code', employee.code);
        option.setAttribute('data-name', employee.full_name);
        datalist.appendChild(option);
      });
      
      // إذا كان هناك تطابق تام، املأ حقول الموظف
      if (isSearchByCode) {
        const exactMatch = searchResults.find(emp => emp.code.toString() === searchInput);
        if (exactMatch) {
          fillEmployeeFields(exactMatch.code, exactMatch.full_name);
        }
      }
    }
  } catch (error) {
    console.error('خطأ في البحث عن الموظف:', error);
    
    // البحث المحلي كبديل
    datalist.innerHTML = '';
    
    let localResults = [];
    if (isSearchByCode) {
      localResults = employees.filter(emp => emp.code.toString().includes(searchInput));
    } else {
      localResults = employees.filter(emp =>
        emp.full_name.toLowerCase().includes(searchInput.toLowerCase())
      );
    }
    
    localResults.forEach(employee => {
      const option = document.createElement('option');
      option.value = employee.full_name;
      option.setAttribute('data-code', employee.code);
      option.setAttribute('data-name', employee.full_name);
      datalist.appendChild(option);
    });
    
    // إذا كان هناك تطابق تام، املأ حقول الموظف
    if (isSearchByCode) {
      const exactMatch = localResults.find(emp => emp.code.toString() === searchInput);
      if (exactMatch) {
        fillEmployeeFields(exactMatch.code, exactMatch.full_name);
      }
    }
  }
}

// ملء حقول الموظف
function fillEmployeeFields(code, name) {
  const employeeCodeInput = document.getElementById('employeeCode');
  const employeeNameInput = document.getElementById('employeeName');
  
  employeeCodeInput.value = code;
  employeeNameInput.value = name;
}

// حفظ المساهمة
async function saveContribution() {
  // التحقق من صحة البيانات
  const employeeCode = document.getElementById('employeeCode').value.trim();
  const employeeName = document.getElementById('employeeName').value.trim();
  const contributionType = document.getElementById('contributionType').value;
  const companyAmount = document.getElementById('companyAmount').value;
  const fundAmount = document.getElementById('fundAmount').value;
  const contributionDate = document.getElementById('contributionDate').value;
  const notes = document.getElementById('notes').value.trim();
  
  if (!employeeCode || !employeeName || !contributionType || !companyAmount || !fundAmount || !contributionDate) {
    showNotification('يرجى ملء جميع الحقول المطلوبة والتأكد من اختيار موظف من قائمة البحث', 'error');
    return;
  }
  
  // التحقق من أن المبالغ أرقام صحيحة
  if (isNaN(parseFloat(companyAmount)) || isNaN(parseFloat(fundAmount))) {
    showNotification('يرجى إدخال مبالغ صحيحة', 'error');
    return;
  }
  
  // التحقق من أن المبالغ أكبر من الصفر
  if (parseFloat(companyAmount) <= 0 || parseFloat(fundAmount) <= 0) {
    showNotification('يجب أن تكون المبالغ أكبر من الصفر', 'error');
    return;
  }

  // التحقق من عدم وجود مساهمة مكررة لنفس الموظف ونفس النوع في نفس التاريخ
  const duplicateCheck = checkDuplicateContribution(employeeCode, contributionType, contributionDate);
  if (duplicateCheck.isDuplicate) {
    const existingContribution = duplicateCheck.contribution;
    let warningMessage = `تحذير: يوجد مساهمة مماثلة للموظف ${employeeName} من نوع "${getContributionTypeText(contributionType)}" في تاريخ ${formatDate(contributionDate)}`;

    if (existingContribution.id) {
      warningMessage += `\n(كود المساهمة: ${existingContribution.id})`;
    }

    warningMessage += `\n\nهل تريد المتابعة وإضافة مساهمة أخرى؟`;

    if (!confirm(warningMessage)) {
      return;
    }
  }
  
  // إنشاء كائن المساهمة
  const contribution = {
    employee_code: employeeCode,
    employee_name: employeeName,
    contribution_type: contributionType,
    company_amount: parseFloat(companyAmount),
    fund_amount: parseFloat(fundAmount),
    contribution_date: contributionDate,
    notes: notes,
    created_at: new Date().toISOString()
  };
  
  try {
    // إرسال البيانات إلى الخادم
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/contributions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(contribution)
    });

    if (response.ok) {
      const savedContribution = await response.json();
      // إضافة created_at إذا لم يكن موجوداً
      if (!savedContribution.created_at) {
        savedContribution.created_at = new Date().toISOString();
      }
      // إضافة المساهمة في بداية المصفوفة ليظهر في أول الجدول
      contributions.unshift(savedContribution);
      // عرض المساهمات
      displayContributions();
      // إعادة تعيين النموذج
      resetContributionForm();
      // عرض إشعار نجاح
      showNotification('تم حفظ المساهمة بنجاح', 'success');
    } else if (response.status === 409) {
      // التعامل مع التحذير السنوي
      const errorData = await response.json();
      if (errorData.yearly_check) {
        const confirmMessage = `${errorData.error}\n\nهل تريد المتابعة والإضافة رغم التحذير؟`;
        if (confirm(confirmMessage)) {
          // إرسال طلب جديد مع تجاهل التحذير
          await forceAddContribution(contribution);
        }
        return;
      }
    } else {
      const errorText = await response.text();
      try {
        const errorData = JSON.parse(errorText);
        showNotification(`فشل في حفظ المساهمة: ${errorData.error || response.statusText}`, 'error');
      } catch (parseError) {
        showNotification(`فشل في حفظ المساهمة: ${response.status} ${response.statusText}`, 'error');
      }
    }
  } catch (error) {
    console.error('خطأ في حفظ المساهمة:', error);
    showNotification('خطأ في حفظ المساهمة', 'error');
  }
}

// دالة الإضافة القسرية (تجاهل التحذير السنوي)
async function forceAddContribution(contribution) {
  try {
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/contributions/force-add`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        ...contribution,
        ignore_yearly_warning: true
      })
    });

    if (response.ok) {
      const savedContribution = await response.json();
      // إضافة created_at إذا لم يكن موجوداً
      if (!savedContribution.created_at) {
        savedContribution.created_at = new Date().toISOString();
      }
      // إضافة المساهمة في بداية المصفوفة ليظهر في أول الجدول
      contributions.unshift({
        id: savedContribution.id,
        ...contribution,
        created_at: savedContribution.created_at
      });
      // عرض المساهمات
      displayContributions();
      // إعادة تعيين النموذج
      resetContributionForm();
      // عرض إشعار نجاح
      showNotification('تم حفظ المساهمة بنجاح (تم تجاهل التحذير)', 'success');
    } else {
      const errorText = await response.text();
      try {
        const errorData = JSON.parse(errorText);
        showNotification(`فشل في حفظ المساهمة: ${errorData.error || response.statusText}`, 'error');
      } catch (parseError) {
        showNotification(`فشل في حفظ المساهمة: ${response.status} ${response.statusText}`, 'error');
      }
    }
  } catch (error) {
    console.error('خطأ في الإضافة القسرية للمساهمة:', error);
    showNotification('حدث خطأ أثناء حفظ المساهمة', 'error');
  }
}

// إعادة تعيين نموذج المساهمة
function resetContributionForm() {
  document.getElementById('employeeSearch').value = '';
  document.getElementById('employeeCode').value = '';
  document.getElementById('employeeName').value = '';
  document.getElementById('contributionType').value = '';
  document.getElementById('companyAmount').value = '';
  document.getElementById('fundAmount').value = '';
  document.getElementById('contributionDate').value = '';
  document.getElementById('notes').value = '';
  
  // مسح الاقتراحات
  const datalist = document.getElementById('employeeSearchSuggestions');
  if (datalist) {
    datalist.innerHTML = '';
  }
}

// عرض المساهمات
function displayContributions() {
  const contributionTableBody = document.getElementById('contributionTableBody');
  if (!contributionTableBody) return;
  
  contributionTableBody.innerHTML = '';
  
  if (contributions.length === 0) {
    const emptyRow = document.createElement('tr');
    emptyRow.innerHTML = `<td colspan="7" style="text-align: center;">لا توجد مساهمات</td>`;
    contributionTableBody.appendChild(emptyRow);
    return;
  }
  
  // ترتيب المساهمات حسب تاريخ الإنشاء (الأحدث أولاً)
  const sortedContributions = [...contributions].sort((a, b) => {
    // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
    const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
    const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
    return bTime - aTime; // الأحدث أولاً
  });

  sortedContributions.forEach((contribution, index) => {
    const row = document.createElement('tr');

    // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
    if (index === 0) {
      row.style.backgroundColor = '#e8f5e8';
      row.style.border = '2px solid #4CAF50';
    }

    // الحصول على اسم نوع المساهمة
    const contributionTypeName = getContributionTypeName(contribution.contribution_type);

    // تحويل المبالغ إلى أرقام للتأكد من عمل toFixed()
    const companyAmount = parseFloat(contribution.company_amount) || 0;
    const fundAmount = parseFloat(contribution.fund_amount) || 0;

    row.innerHTML = `
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${contribution.employee_code}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${contribution.employee_name}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${contributionTypeName}</td>
      <td class="company-amount" style="${index === 0 ? 'font-weight: bold;' : ''}">${companyAmount.toFixed(2)}</td>
      <td class="fund-amount" style="${index === 0 ? 'font-weight: bold;' : ''}">${fundAmount.toFixed(2)}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${formatDate(contribution.contribution_date)}</td>
      <td style="${index === 0 ? 'font-weight: bold;' : ''}">${contribution.notes || '-'}</td>
      <td class="table-actions">
        ${hasPermission('edit_contribution') ? `<button class="edit-contribution-btn" data-id="${contribution.id}">تعديل</button>` : ''}
        ${hasPermission('delete_contribution') ? `<button class="delete-contribution-btn" data-id="${contribution.id}">حذف</button>` : ''}
      </td>
    `;

    contributionTableBody.appendChild(row);
  });
  
  // إضافة مستمعي الأحداث لأزرار التعديل والحذف
  addEditDeleteEventListeners();
  
  // تطبيق الصلاحيات على الأزرار
  if (window.permissionManager) {
    window.permissionManager.applyPermissions();
  } else if (typeof applyPermissions === 'function') {
    applyPermissions();
  }
}

// فحص الصلاحيات
function hasPermission(permission) {
  try {
    const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
    const result = permissions[permission] === true;
    console.log(`[Contribution] hasPermission(${permission}) = ${result}`, permissions);
    return result;
  } catch (error) {
    console.error('خطأ في قراءة الصلاحيات:', error);
    return false;
  }
}

// إضافة مستمعي الأحداث لأزرار التعديل والحذف
function addEditDeleteEventListeners() {
  // أزرار التعديل
  const editButtons = document.querySelectorAll('.edit-contribution-btn');
  editButtons.forEach(button => {
    // التحقق من وجود صلاحية التعديل
    if (hasPermission('edit_contribution')) {
      // إضافة مستمع الحدث باستخدام وظيفة مجهولة
      button.onclick = function(event) {
        event.preventDefault();
        event.stopPropagation();
        const contributionId = this.getAttribute('data-id');
        console.log('تم النقر على زر التعديل للمساهمة رقم:', contributionId);
        openEditModal(contributionId);
      };
    } else {
      // تعطيل الزر إذا لم تكن هناك صلاحية
      button.disabled = true;
      button.classList.add('disabled');
      button.style.opacity = '0.5';
      button.style.cursor = 'not-allowed';
      button.title = 'ليس لديك صلاحية لهذا الإجراء';
    }
  });
  
  // أزرار الحذف
  const deleteButtons = document.querySelectorAll('.delete-contribution-btn');
  deleteButtons.forEach(button => {
    // التحقق من وجود صلاحية الحذف
    if (hasPermission('delete_contribution')) {
      // إضافة مستمع الحدث باستخدام وظيفة مجهولة
      button.onclick = function(event) {
      event.preventDefault();
      event.stopPropagation();
      const contributionId = this.getAttribute('data-id');
      console.log('تم النقر على زر الحذف للمساهمة رقم:', contributionId);
      confirmDeleteContribution(contributionId);
    };
    } else {
      // تعطيل الزر إذا لم تكن هناك صلاحية
      button.disabled = true;
      button.classList.add('disabled');
      button.style.opacity = '0.5';
      button.style.cursor = 'not-allowed';
      button.title = 'ليس لديك صلاحية لهذا الإجراء';
    }
  });
}

// فتح نافذة التعديل
function openEditModal(contributionId) {
  // فحص صلاحية التعديل
  if (!hasPermission('edit_contribution')) {
    alert('ليس لديك صلاحية لتعديل المساهمات');
    return;
  }

  const contribution = contributions.find(c => c.id == contributionId);
  if (!contribution) return;
  
  console.log('فتح نافذة تعديل المساهمة:', contribution);
  
  // الحصول على النافذة المنبثقة أولاً
  const modal = document.getElementById('editContributionModal');
  if (!modal) {
    console.error('لم يتم العثور على نافذة التعديل');
    return;
  }
  
  try {
    // ملء النموذج بالبيانات
    document.getElementById('editContributionId').value = contribution.id;
    document.getElementById('editEmployeeCode').value = contribution.employee_code;
    document.getElementById('editEmployeeName').value = contribution.employee_name;
    document.getElementById('editContributionType').value = contribution.contribution_type;
    document.getElementById('editCompanyAmount').value = contribution.company_amount;
    document.getElementById('editFundAmount').value = contribution.fund_amount;
    // حذف الإشارة إلى حقل القسم غير الموجود
    // document.getElementById('editDepartment').value = contribution.department || '';
    // استخدام DateUtils لتنسيق التاريخ بشكل صحيح
    if (typeof DateUtils !== 'undefined') {
      document.getElementById('editContributionDate').value = DateUtils.formatDateForInput(contribution.contribution_date);
    } else {
      document.getElementById('editContributionDate').value = formatDateForInput(contribution.contribution_date);
    }
    document.getElementById('editNotes').value = contribution.notes || '';
    
    // إظهار النافذة المنبثقة
    modal.style.display = 'block';
    
    // إضافة مستمع الحدث لزر التحديث
    const updateBtn = document.getElementById('updateContribution');
    updateBtn.onclick = function() {
      updateContribution();
    };
  } catch (error) {
    console.error('خطأ في فتح نافذة التعديل:', error);
    alert('حدث خطأ أثناء محاولة فتح نافذة التعديل. يرجى المحاولة مرة أخرى.');
    return;
  }
  
  // إضافة مستمع الحدث لزر الإلغاء وزر الإغلاق
  const closeBtn = modal.querySelector('.close');
  const cancelBtn = modal.querySelector('.cancel-btn');
  
  if (closeBtn) {
    closeBtn.onclick = function() {
      modal.style.display = 'none';
    };
  }
  
  if (cancelBtn) {
    cancelBtn.onclick = function(e) {
      e.preventDefault();
      modal.style.display = 'none';
    };
  }
  
  // إغلاق النافذة عند النقر خارجها (محسن لتجنب التعارض مع القائمة الجانبية)
  modal.addEventListener('click', function(event) {
    if (event.target === modal) {
      modal.style.display = 'none';
    }
  });
  
  // إضافة مستمع حدث للنموذج لمنع السلوك الافتراضي
  const editForm = document.getElementById('editContributionForm');
  if (editForm) {
    editForm.addEventListener('submit', function(e) {
      e.preventDefault();
    });
  }
}

// تحديث المساهمة
async function updateContribution() {
  const contributionId = document.getElementById('editContributionId').value;
  const contributionType = document.getElementById('editContributionType').value;
  const companyAmount = document.getElementById('editCompanyAmount').value;
  const fundAmount = document.getElementById('editFundAmount').value;
  const contributionDate = document.getElementById('editContributionDate').value;
  const notes = document.getElementById('editNotes').value.trim();
  
  // التحقق من صحة البيانات باستخدام FormValidator
  if (typeof FormValidator !== 'undefined') {
    const form = document.getElementById('editContributionForm');
    const validator = new FormValidator(form);

    // التحقق من صحة البيانات
    validator.validateRequired('editContributionType', 'نوع المساهمة');
    validator.validateNumber('editCompanyAmount', 'مبلغ مساهمة الشركة', 0, null, true);
    validator.validateNumber('editFundAmount', 'مبلغ مساهمة صندوق الزمالة', 0, null, true);
    validator.validateDate('editContributionDate', 'تاريخ المساهمة', true);

    // التحقق من أن التاريخ ليس في المستقبل
    if (contributionDate && !ValidationHelpers.validateNotFutureDate(contributionDate)) {
      validator.addError('editContributionDate', 'تاريخ المساهمة لا يمكن أن يكون في المستقبل');
    }

    // عرض الأخطاء إن وجدت
    if (!validator.displayErrors()) {
      return;
    }
  } else {
    // التحقق التقليدي كـ fallback
    if (!contributionType || !companyAmount || !fundAmount || !contributionDate) {
      showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
      return;
    }
  }

  // الحصول على بيانات المساهمة الحالية
  const currentContribution = contributions.find(c => c.id == contributionId);
  if (!currentContribution) {
    showNotification('لم يتم العثور على المساهمة', 'error');
    return;
  }

  // التحقق من عدم وجود مساهمة مكررة (باستثناء المساهمة الحالية)
  const duplicateCheck = checkDuplicateContributionForUpdate(
    currentContribution.employee_code,
    contributionType,
    contributionDate,
    contributionId
  );

  if (duplicateCheck.isDuplicate) {
    const existingContribution = duplicateCheck.contribution;
    let warningMessage = `تحذير: يوجد مساهمة مماثلة للموظف ${currentContribution.employee_name} من نوع "${getContributionTypeText(contributionType)}" في تاريخ ${formatDate(contributionDate)}`;

    if (existingContribution.id) {
      warningMessage += `\n(كود المساهمة: ${existingContribution.id})`;
    }

    warningMessage += `\n\nهل تريد المتابعة وتحديث المساهمة؟`;

    if (!confirm(warningMessage)) {
      return;
    }
  }

  // إنشاء كائن التحديث
  const updatedContribution = {
    contribution_type: contributionType,
    company_amount: parseFloat(companyAmount),
    fund_amount: parseFloat(fundAmount),
    contribution_date: contributionDate,
    notes: notes
  };
  
  try {
    console.log('إرسال طلب تحديث المساهمة:', contributionId);
    console.log('بيانات التحديث:', updatedContribution);

    // إضافة مؤشر التحميل
    const updateBtn = document.getElementById('updateContribution');
    const originalText = updateBtn.textContent;
    updateBtn.textContent = 'جاري التحديث...';
    updateBtn.disabled = true;

    // إرسال البيانات إلى الخادم
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/contributions/${contributionId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updatedContribution)
    });

    console.log('استجابة التحديث:', response.status, response.statusText);
    
    if (response.ok) {
      // تحديث المساهمة في المصفوفة المحلية
      const index = contributions.findIndex(c => c.id == contributionId);
      if (index !== -1) {
        contributions[index] = { ...contributions[index], ...updatedContribution };
      }
      
      // إغلاق النافذة المنبثقة
      document.getElementById('editContributionModal').style.display = 'none';
      
      // إعادة عرض المساهمات
      displayContributions();
      
      // عرض إشعار نجاح
      showNotification('تم تحديث المساهمة بنجاح', 'success');
    } else {
      const errorData = await response.json();
      showNotification(`فشل في تحديث المساهمة: ${errorData.error || response.statusText}`, 'error');
    }
  } catch (error) {
    console.error('خطأ في تحديث المساهمة:', error);
    showNotification('خطأ في تحديث المساهمة', 'error');
  } finally {
    // إعادة تعيين زر التحديث
    const updateBtn = document.getElementById('updateContribution');
    if (updateBtn) {
      updateBtn.textContent = 'تحديث';
      updateBtn.disabled = false;
    }
  }
}

// تأكيد حذف المساهمة
function confirmDeleteContribution(contributionId) {
  if (confirm('هل أنت متأكد من حذف هذه المساهمة؟')) {
    deleteContribution(contributionId);
  }
}

// حذف المساهمة
async function deleteContribution(contributionId) {
  // فحص صلاحية الحذف
  if (!hasPermission('delete_contribution')) {
    alert('ليس لديك صلاحية لحذف المساهمات');
    return;
  }

  try {
    console.log('إرسال طلب حذف المساهمة:', contributionId);

    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/contributions/${contributionId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('استجابة الحذف:', response.status, response.statusText);
    
    if (response.ok) {
      // حذف المساهمة من المصفوفة المحلية
      contributions = contributions.filter(c => c.id != contributionId);
      
      // إعادة عرض المساهمات
      displayContributions();
      
      // عرض إشعار نجاح
      showNotification('تم حذف المساهمة بنجاح', 'success');
    } else {
      const errorData = await response.json();
      showNotification(`فشل في حذف المساهمة: ${errorData.error || response.statusText}`, 'error');
    }
  } catch (error) {
    console.error('خطأ في حذف المساهمة:', error);
    showNotification('خطأ في حذف المساهمة', 'error');
  }
}

// البحث في المساهمات (بحث مباشر من قاعدة البيانات)
async function searchContributions() {
  try {
    const employeeSearch = document.getElementById('searchEmployeeName')?.value.trim() || '';
    const contributionType = document.getElementById('searchContributionType')?.value || '';
    const startDate = document.getElementById('searchStartDate')?.value || '';
    const endDate = document.getElementById('searchEndDate')?.value || '';
    
    // بناء URL للبحث مع المعلمات
    let searchUrl = `${API_URL}/contributions/search?`;
    const searchParams = new URLSearchParams();
    
    // التحقق مما إذا كان البحث بالكود أو بالاسم
    if (employeeSearch) {
      const isSearchByCode = /^\d+$/.test(employeeSearch);
      
      if (isSearchByCode) {
        searchParams.append('employee_code', employeeSearch);
      } else {
        searchParams.append('employee_name', employeeSearch);
      }
    }
    
    if (contributionType) {
      searchParams.append('contribution_type', contributionType);
    }
    
    if (startDate) {
      searchParams.append('start_date', startDate);
    }
    
    if (endDate) {
      searchParams.append('end_date', endDate);
    }
    
    searchUrl += searchParams.toString();
    console.log('URL البحث المباشر من قاعدة البيانات:', searchUrl);
    
    // إظهار مؤشر التحميل
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) loadingIndicator.style.display = 'block';
    
    // إرسال طلب البحث إلى الخادم
    const token = localStorage.getItem('token');
    const response = await fetch(searchUrl, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    // إخفاء مؤشر التحميل
    if (loadingIndicator) loadingIndicator.style.display = 'none';
    
    if (response.ok) {
      const searchResults = await response.json();
      console.log('نتائج البحث من قاعدة البيانات:', searchResults.length, 'مساهمة');
      
      // تحديث المصفوفة المحلية بنتائج البحث
      // هذا يضمن أن لدينا أحدث البيانات من قاعدة البيانات
      if (searchResults && searchResults.length > 0) {
        // عرض النتائج
        displayFilteredContributions(searchResults);
        
        // تمكين زر تصدير Excel
        const exportFilteredBtn = document.getElementById('exportFilteredBtn');
        if (exportFilteredBtn) {
          exportFilteredBtn.onclick = function() {
            exportToExcel('filteredContributionsTable', 'المساهمات_المفلترة');
          };
        }
      } else {
        // عرض رسالة لا توجد نتائج
        displayFilteredContributions([]);
      }
    } else {
      console.error('خطأ في استجابة البحث:', response.status, response.statusText);
      // في حالة حدوث خطأ في الاستجابة، استخدم التصفية المحلية
      const errorData = await response.json();
      showNotification(`فشل في البحث: ${errorData.error || response.statusText}`, 'error');
      
      // استخدام التصفية المحلية كخطة بديلة
      let filteredContributions = [...contributions];
      
      if (employeeSearch) {
        const isSearchByCode = /^\d+$/.test(employeeSearch);
        if (isSearchByCode) {
          filteredContributions = filteredContributions.filter(c => c.employee_code.toString().includes(employeeSearch));
        } else {
          filteredContributions = filteredContributions.filter(c => c.employee_name && c.employee_name.toLowerCase().includes(employeeSearch.toLowerCase()));
        }
      }
      
      if (contributionType) {
        // تحويل كلا القيمتين إلى نص للمقارنة
        filteredContributions = filteredContributions.filter(c => String(c.contribution_type) === String(contributionType));
      }
      
      if (startDate) {
        const filterStartDate = new Date(startDate);
        filterStartDate.setHours(0, 0, 0, 0);
        filteredContributions = filteredContributions.filter(c => {
          const contributionDate = new Date(c.contribution_date);
          contributionDate.setHours(0, 0, 0, 0);
          return contributionDate >= filterStartDate;
        });
      }
      
      if (endDate) {
        const filterEndDate = new Date(endDate);
        filterEndDate.setHours(23, 59, 59, 999);
        filteredContributions = filteredContributions.filter(c => {
          const contributionDate = new Date(c.contribution_date);
          contributionDate.setHours(0, 0, 0, 0);
          return contributionDate <= filterEndDate;
        });
      }
      
      displayFilteredContributions(filteredContributions);
    }
  } catch (error) {
    console.error('خطأ في البحث عن المساهمات:', error);
    showNotification('خطأ في البحث عن المساهمات', 'error');
    
    // إخفاء مؤشر التحميل في حالة الخطأ
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) loadingIndicator.style.display = 'none';
    
    // استخدام التصفية المحلية كخطة بديلة
    let filteredContributions = [...contributions];
    displayFilteredContributions(filteredContributions);
  }
}

// البحث في قائمة المساهمات
async function searchContributionsFromList() {
  try {
    const employeeSearch = document.getElementById('searchContributionEmployee')?.value.trim() || '';
    const contributionType = document.getElementById('searchContributionType')?.value || '';
    const startDate = document.getElementById('searchContributionStartDate')?.value || '';
    const endDate = document.getElementById('searchContributionEndDate')?.value || '';

    const params = new URLSearchParams();

    if (employeeSearch) {
      params.append('employee_name', employeeSearch);
      params.append('employee_code', employeeSearch);
    }
    if (contributionType) params.append('contribution_type', contributionType);
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    let url = `${API_URL}/contributions`;
    if (params.toString()) {
      url = `${API_URL}/contributions/search?${params.toString()}`;
    }

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error('فشل في جلب بيانات المساهمات');
    }

    const filteredContributions = await response.json();
    displayContributions(filteredContributions);

  } catch (error) {
    console.error('خطأ في البحث:', error);
    showNotification('خطأ في البحث عن المساهمات', 'error');
  }
}

// مسح البحث في قائمة المساهمات
function clearContributionSearch() {
  document.getElementById('searchContributionEmployee').value = '';
  document.getElementById('searchContributionType').value = '';
  document.getElementById('searchContributionStartDate').value = '';
  document.getElementById('searchContributionEndDate').value = '';

  // عرض جميع المساهمات
  loadContributions();
}

// عرض المساهمات المفلترة
function displayFilteredContributions(filteredContributions) {
  const filteredTableBody = document.getElementById('filteredContributionsTableBody');
  if (!filteredTableBody) return;
  
  filteredTableBody.innerHTML = '';
  
  if (filteredContributions.length === 0) {
    const emptyRow = document.createElement('tr');
    emptyRow.innerHTML = `<td colspan="7" style="text-align: center;">لا توجد نتائج</td>`;
    filteredTableBody.appendChild(emptyRow);
    return;
  }
  
  // ترتيب المساهمات حسب التاريخ (الأحدث أولاً)
  const sortedContributions = [...filteredContributions].sort((a, b) => {
    return new Date(b.contribution_date) - new Date(a.contribution_date);
  });
  
  sortedContributions.forEach(contribution => {
    const row = document.createElement('tr');
    
    // الحصول على اسم نوع المساهمة
    const contributionTypeName = getContributionTypeName(contribution.contribution_type);
    
    // تحويل المبالغ إلى أرقام للتأكد من عمل toFixed()
    const companyAmount = parseFloat(contribution.company_amount) || 0;
    const fundAmount = parseFloat(contribution.fund_amount) || 0;
    
    row.innerHTML = `
      <td>${contribution.employee_code}</td>
      <td>${contribution.employee_name}</td>
      <td>${contributionTypeName}</td>
      <td class="company-amount">${companyAmount.toFixed(2)}</td>
      <td class="fund-amount">${fundAmount.toFixed(2)}</td>
      <td>${formatDate(contribution.contribution_date)}</td>
      <td>${contribution.notes || '-'}</td>
    `;
    
    filteredTableBody.appendChild(row);
  });
  
  // إضافة زر تصدير إلى Excel إذا كانت هناك نتائج
  const exportBtnContainer = document.getElementById('filteredExportBtnContainer');
  if (exportBtnContainer) {
    exportBtnContainer.style.display = filteredContributions.length > 0 ? 'block' : 'none';
  }
  
  // تطبيق الصلاحيات على الأزرار
  if (typeof applyPermissions === 'function') {
    applyPermissions();
  }
}

// إعادة تعيين البحث
function resetContributionsSearch() {
  document.getElementById('searchEmployeeName').value = '';
  document.getElementById('searchContributionType').value = '';
  document.getElementById('searchStartDate').value = '';
  document.getElementById('searchEndDate').value = '';
  
  // عرض جميع المساهمات
  displayFilteredContributions(contributions);
}

// دالة مساعدة للبحث في التواريخ
function searchInDate(dateText, searchTerm) {
  if (!dateText || !searchTerm) return false;

  // البحث في النص الأصلي للتاريخ
  if (dateText.toLowerCase().includes(searchTerm.toLowerCase())) {
    return true;
  }

  // محاولة تحويل التاريخ لصيغ مختلفة للبحث
  try {
    const date = new Date(dateText);
    if (!isNaN(date.getTime())) {
      // البحث في السنة
      const year = date.getFullYear().toString();
      if (year.includes(searchTerm)) return true;

      // البحث في الشهر
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      if (month.includes(searchTerm)) return true;

      // البحث في اليوم
      const day = date.getDate().toString().padStart(2, '0');
      if (day.includes(searchTerm)) return true;

      // البحث في التاريخ بصيغة مختلفة
      const formattedDate = `${year}-${month}-${day}`;
      if (formattedDate.includes(searchTerm)) return true;

      const arabicDate = `${day}/${month}/${year}`;
      if (arabicDate.includes(searchTerm)) return true;
    }
  } catch (e) {
    // في حالة فشل تحويل التاريخ، استخدم البحث النصي العادي
  }

  return false;
}

// دالة مساعدة للبحث المحسن في النصوص
function enhancedTextSearch(text, searchTerm) {
  if (!text || !searchTerm) return false;

  const textLower = text.toLowerCase().trim();
  const searchLower = searchTerm.toLowerCase().trim();

  // البحث العادي
  if (textLower.includes(searchLower)) return true;

  // البحث بالكلمات المنفصلة
  const searchWords = searchLower.split(/\s+/);
  const textWords = textLower.split(/\s+/);

  // التحقق من وجود جميع كلمات البحث في النص
  return searchWords.every(searchWord =>
    textWords.some(textWord => textWord.includes(searchWord))
  );
}

// تصفية جدول المساهمات المحسنة
function filterContributionsTable(searchTerm, tableBodyId) {
  const tableBody = document.getElementById(tableBodyId);
  const rows = tableBody.querySelectorAll('tr');

  // إذا كان البحث فارغًا، أظهر جميع الصفوف
  if (!searchTerm || searchTerm.trim() === '') {
    rows.forEach(row => {
      row.style.display = '';
    });
    return;
  }

  const searchTermLower = searchTerm.toLowerCase().trim();

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    if (cells.length === 0) return; // تجاهل الصفوف الفارغة

    // استخراج البيانات من الخلايا
    const employeeCode = cells[0]?.textContent?.trim() || '';
    const employeeName = cells[1]?.textContent?.trim() || '';
    const contributionType = cells[2]?.textContent?.trim() || '';
    const companyAmount = cells[3]?.textContent?.trim() || '';
    const fundAmount = cells[4]?.textContent?.trim() || '';
    const contributionDate = cells[5]?.textContent?.trim() || '';
    const notes = cells[6]?.textContent?.trim() || '';

    // البحث في الحقول المختلفة باستخدام البحث المحسن
    const matchesCode = enhancedTextSearch(employeeCode, searchTermLower);
    const matchesName = enhancedTextSearch(employeeName, searchTermLower);
    const matchesType = enhancedTextSearch(contributionType, searchTermLower);
    const matchesCompanyAmount = enhancedTextSearch(companyAmount, searchTermLower);
    const matchesFundAmount = enhancedTextSearch(fundAmount, searchTermLower);
    const matchesDate = searchInDate(contributionDate, searchTermLower);
    const matchesNotes = enhancedTextSearch(notes, searchTermLower);

    // إظهار الصف إذا تطابق مع أي من الحقول
    if (matchesCode || matchesName || matchesType || matchesCompanyAmount ||
        matchesFundAmount || matchesDate || matchesNotes) {
      row.style.display = '';
    } else {
      row.style.display = 'none';
    }
  });
}

// الحصول على اسم نوع المساهمة
function getContributionTypeName(typeId) {
  // إذا كان نوع المساهمة نصي (من القائمة المنسدلة) نعيده كما هو
  if (typeof typeId === 'string' && isNaN(parseInt(typeId))) {
    return typeId;
  }
  
  // إذا كان رقمي نستخدم القاموس
  const types = {
    '1': 'الزواج',
    '2': 'إنجاب الطفل',
    '3': 'الولادة الطبيعية للزوجة',
    '4': 'الولادة القيصرية للزوجة',
    '5': 'إصابة العمل',
    '6': 'العمليات الجراحية للعامل الغير مؤمن عليه',
    '7': 'العمليات الجراحية للزوجة والأبناء',
    '8': 'المرضى دون الأمراض المزمنة',
    '9': 'الوفاة المؤمن عليه اجتماعيا',
    '10': 'وفاة الموظف في حالة أنه غير مؤمن عليه اجتماعيا',
    '11': 'وفاة أحد الأقارب من الدرجة الأولى',
    '12': 'زواج أحد أبناء العاملين'
  };
  
  return types[typeId] || 'غير معروف';
}

// تنسيق التاريخ
function formatDate(dateString) {
  if (typeof DateUtils !== 'undefined') {
    return DateUtils.formatDateFromDatabase(dateString);
  }
  return '';
}

// تنسيق التاريخ لحقل الإدخال
function formatDateForInput(dateString) {
  // استخدام مكتبة التواريخ المركزية
  if (typeof DateUtils !== 'undefined') {
    return DateUtils.formatDateForInput(dateString);
  }

  // Fallback للحالات القديمة
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// تصدير إلى Excel
function exportToExcel(tableId, fileName) {
  console.log('جاري تصدير الجدول:', tableId);
  const table = document.getElementById(tableId);
  if (!table) {
    console.error('لم يتم العثور على الجدول:', tableId);
    
    // عرض جميع الجداول الموجودة في الصفحة للتصحيح
    const allTables = document.querySelectorAll('table');
    console.log(`عدد الجداول الموجودة في الصفحة: ${allTables.length}`);
    allTables.forEach((t, index) => {
      console.log(`جدول ${index + 1} - المعرف: ${t.id || 'بدون معرف'}, الفئة: ${t.className}`);
    });
    
    showNotification('لم يتم العثور على الجدول المطلوب تصديره: ' + tableId, 'error');
    return;
  }
  
  try {
    // تحويل الجدول إلى مصفوفة بيانات
    const data = [];
    
    // إضافة رؤوس الأعمدة
    const headers = [];
    const headerCells = table.querySelectorAll('thead th');
    headerCells.forEach(cell => {
      headers.push(cell.textContent.trim());
    });
    data.push(headers);
    
    // إضافة صفوف البيانات
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
      // تجاهل الصفوف المخفية (المفلترة)
      if (row.style.display !== 'none') {
        const rowData = [];
        const cells = row.querySelectorAll('td');
        cells.forEach(cell => {
          // تنظيف النص من أي علامات HTML
          let cellText = cell.textContent.trim();
          
          // تحويل الأرقام بتنسيق عربي إلى تنسيق إنجليزي للأرقام
          if (!isNaN(parseFloat(cellText.replace(/[^0-9.-]/g, '')))) {
            const numericValue = parseFloat(cellText.replace(/[^0-9.-]/g, ''));
            rowData.push(numericValue);
          } else {
            rowData.push(cellText);
          }
        });
        data.push(rowData);
      }
    });
    
    // إنشاء ورقة عمل من البيانات
    const ws = XLSX.utils.aoa_to_sheet(data);
    
    // تعيين عرض الأعمدة بشكل مناسب
    const colWidths = [];
    headers.forEach((header, index) => {
      // تحديد عرض العمود بناءً على محتوى العمود
      let maxWidth = header.length;
      data.forEach(row => {
        if (row[index] && String(row[index]).length > maxWidth) {
          maxWidth = String(row[index]).length;
        }
      });
      // تعيين عرض العمود (مع إضافة هامش)
      colWidths.push({ wch: Math.min(maxWidth + 2, 50) });
    });
    ws['!cols'] = colWidths;
    
    // تعيين اتجاه النص من اليمين إلى اليسار
    ws['!rtl'] = true;
    
    // إنشاء كتاب عمل وإضافة ورقة العمل إليه
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
    
    // تصدير الملف
    XLSX.writeFile(wb, `${fileName}.xlsx`);
    
    showNotification('تم تصدير البيانات بنجاح إلى ملف Excel', 'success');
  } catch (error) {
    console.error('خطأ في تصدير البيانات إلى Excel:', error);
    showNotification('حدث خطأ أثناء تصدير البيانات إلى Excel', 'error');
  }
}

// البحث في أسماء الموظفين أثناء الكتابة
async function handleEmployeeNameSearch() {
  const employeeNameInput = document.getElementById('employeeName');
  const employeeCodeInput = document.getElementById('employeeCode');
  const searchTerm = employeeNameInput.value.trim();
  const datalist = document.getElementById('employeeNameSuggestions');
  
  if (searchTerm.length < 2) {
    employeeCodeInput.value = '';
    // مسح الاقتراحات عند عدم وجود نص كافي للبحث
    if (datalist) {
      datalist.innerHTML = '';
    }
    return;
  }
  
  try {
    // البحث في قاعدة البيانات
    const token = localStorage.getItem('token');
    const response = await fetch(`${API_URL}/employees?search=${encodeURIComponent(searchTerm)}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    if (response.ok) {
      const searchResults = await response.json();
      
      // تحديث الاقتراحات بنتائج البحث
      const datalist = document.getElementById('employeeNameSuggestions');
      if (datalist) {
        datalist.innerHTML = '';
        
        searchResults.forEach(employee => {
          const option = document.createElement('option');
          option.value = employee.full_name;
          option.setAttribute('data-code', employee.code);
          datalist.appendChild(option);
        });
      }
      
      // إذا كان هناك تطابق تام، املأ كود الموظف
      const exactMatch = searchResults.find(emp => 
        emp.full_name.toLowerCase() === searchTerm.toLowerCase()
      );
      
      if (exactMatch) {
        employeeCodeInput.value = exactMatch.code;
      } else {
        employeeCodeInput.value = '';
      }
    }
  } catch (error) {
    console.error('خطأ في البحث عن الموظف:', error);
    
    // البحث المحلي كبديل
    const localResults = employees.filter(emp => 
      emp.full_name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    const datalist = document.getElementById('employeeNameSuggestions');
    if (datalist) {
      datalist.innerHTML = '';
      
      localResults.forEach(employee => {
        const option = document.createElement('option');
        option.value = employee.full_name;
        option.setAttribute('data-code', employee.code);
        datalist.appendChild(option);
      });
    }
    
    const exactMatch = localResults.find(emp => 
      emp.full_name.toLowerCase() === searchTerm.toLowerCase()
    );
    
    if (exactMatch) {
      employeeCodeInput.value = exactMatch.code;
    } else {
      employeeCodeInput.value = '';
    }
  }
 }
 
 // التعامل مع اختيار الموظف من الاقتراحات
function handleEmployeeSelection() {
  const searchInput = document.getElementById('employeeSearch');
  const selectedValue = searchInput.value.trim();
  
  if (!selectedValue) {
    // مسح الحقول إذا كان حقل البحث فارغًا
    document.getElementById('employeeCode').value = '';
    document.getElementById('employeeName').value = '';
    return;
  }
  
  // البحث في خيارات datalist
  const datalist = document.getElementById('employeeSearchSuggestions');
  if (datalist) {
    const options = datalist.querySelectorAll('option');
    for (let option of options) {
      if (option.value === selectedValue) {
        const code = option.getAttribute('data-code');
        const name = option.getAttribute('data-name');
        if (code && name) {
          fillEmployeeFields(code, name);
          return;
        }
      }
    }
  }
  
  // البحث عن الموظف بالاسم الكامل في المصفوفة المحلية
  const employee = employees.find(emp =>
    emp.full_name.toLowerCase() === selectedValue.toLowerCase()
  );

  if (employee) {
    fillEmployeeFields(employee.code, employee.full_name);
  } else {
    // إذا كان النص يبدو كأنه كود موظف، حاول البحث عنه
    if (/^\d+$/.test(selectedValue)) {
      const employeeByCode = employees.find(emp => emp.code.toString() === selectedValue);
      if (employeeByCode) {
        fillEmployeeFields(employeeByCode.code, employeeByCode.full_name);
      }
    }
  }
}
 
 // استخدام دالة showNotification من shared-utils.js

// توليد تقرير المساهمات
async function generateContributionReport() {
  const reportStartDate = document.getElementById('reportStartDate').value;
  const reportEndDate = document.getElementById('reportEndDate').value;
  
  if (!reportStartDate || !reportEndDate) {
    showNotification('الرجاء تحديد فترة التقرير (من تاريخ وإلى تاريخ)');
    return;
  }
  
  // التحقق من أن تاريخ البداية لا يتجاوز تاريخ النهاية
  if (reportStartDate > reportEndDate) {
    showNotification('تاريخ البداية يجب أن يكون قبل أو يساوي تاريخ النهاية');
    return;
  }
  
  // إظهار مؤشر التحميل
  document.getElementById('reportLoading').style.display = 'flex';
  document.getElementById('reportResults').style.display = 'none';
  document.getElementById('noReportDataMessage').style.display = 'none';
  document.getElementById('detailsSection').style.display = 'none';
  
  try {
    // استعلام البحث عن المساهمات في الفترة المحددة
    let searchUrl = `${API_URL}/contributions/search?`;
    searchUrl += `start_date=${reportStartDate}&end_date=${reportEndDate}`;
    
    const token = localStorage.getItem('token');
    const response = await fetch(searchUrl, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    const reportData = await response.json();
    
    if (reportData.length === 0) {
      // لا توجد بيانات للتاريخ المحدد
      document.getElementById('noReportDataMessage').style.display = 'block';
      document.getElementById('reportResults').style.display = 'none';
    } else {
      // حساب إجمالي المساهمات
      let totalCompanyAmount = 0;
      let totalFundAmount = 0;
      
      // حساب عدد الموظفين المستفيدين (بدون تكرار)
      const uniqueEmployees = new Set();
      const companyBeneficiaryEmployees = new Set();
      const fundBeneficiaryEmployees = new Set();
      
      reportData.forEach(contribution => {
        totalCompanyAmount += parseFloat(contribution.company_amount) || 0;
        totalFundAmount += parseFloat(contribution.fund_amount) || 0;
        
        // إضافة كود الموظف إلى المجموعة إذا كان مستفيدًا من أي مساهمة
        if (parseFloat(contribution.company_amount) > 0 || parseFloat(contribution.fund_amount) > 0) {
          uniqueEmployees.add(contribution.employee_code);
        }
        
        // إضافة كود الموظف إلى مجموعة المستفيدين من مساهمات الشركة
        if (parseFloat(contribution.company_amount) > 0) {
          companyBeneficiaryEmployees.add(contribution.employee_code);
        }
        
        // إضافة كود الموظف إلى مجموعة المستفيدين من مساهمات صندوق الزمالة
        if (parseFloat(contribution.fund_amount) > 0) {
          fundBeneficiaryEmployees.add(contribution.employee_code);
        }
      });
      
      // حساب إجمالي المساهمات (الشركة + صندوق الزمالة)
      const totalContributionsAmount = totalCompanyAmount + totalFundAmount;
      
      // عرض الإجماليات
      document.getElementById('totalContributionsAmount').textContent = totalContributionsAmount.toFixed(2);
      document.getElementById('totalCompanyAmount').textContent = totalCompanyAmount.toFixed(2);
      document.getElementById('totalFundAmount').textContent = totalFundAmount.toFixed(2);
      document.getElementById('totalBeneficiaryEmployees').textContent = uniqueEmployees.size;
      document.getElementById('companyBeneficiaryEmployees').textContent = companyBeneficiaryEmployees.size;
      document.getElementById('fundBeneficiaryEmployees').textContent = fundBeneficiaryEmployees.size;
      
      // تخزين البيانات للاستخدام في عرض التفاصيل
      window.reportData = reportData;
      
      // إظهار نتائج التقرير
      document.getElementById('reportResults').style.display = 'block';
    }
  } catch (error) {
    console.error('خطأ في توليد التقرير:', error);
    showNotification('حدث خطأ أثناء توليد التقرير');
  } finally {
    // إخفاء مؤشر التحميل
    document.getElementById('reportLoading').style.display = 'none';
  }
}

// عرض تفاصيل المساهمات
function showContributionDetails(type) {
  if (!window.reportData || window.reportData.length === 0) {
    showNotification('لا توجد بيانات للعرض');
    return;
  }
  
  const detailsSection = document.getElementById('detailsSection');
  const detailsTitle = document.getElementById('detailsTitle');
  const detailsTableBody = document.getElementById('detailsTableBody');
  const noDetailsMessage = document.getElementById('noDetailsMessage');
  
  // تحديد نوع التفاصيل المطلوبة
  let title = '';
  let amountField = '';
  let showBothAmounts = false;
  
  // الحصول على الفترة الزمنية المحددة
  const startDate = document.getElementById('reportStartDate').value;
  const endDate = document.getElementById('reportEndDate').value;
  const dateRangeText = startDate === endDate ? 
    `بتاريخ ${formatDate(startDate)}` : 
    `من ${formatDate(startDate)} إلى ${formatDate(endDate)}`;
  
  if (type === 'company') {
    title = `تفاصيل مساهمات الشركة ${dateRangeText}`;
    amountField = 'company_amount';
  } else if (type === 'fund') {
    title = `تفاصيل مساهمات صندوق الزمالة ${dateRangeText}`;
    amountField = 'fund_amount';
  } else if (type === 'total') {
    title = `تفاصيل إجمالي المساهمات ${dateRangeText}`;
    showBothAmounts = true;
  }
  
  detailsTitle.textContent = title;
  
  // إضافة معلومات الفترة الزمنية في عنوان النتائج
  const reportPeriodText = startDate === endDate ? 
    `تقرير المساهمات ليوم ${formatDate(startDate)}` : 
    `تقرير المساهمات للفترة من ${formatDate(startDate)} إلى ${formatDate(endDate)}`;
  document.getElementById('reportPeriodTitle').textContent = reportPeriodText;
  
  // تصفية البيانات للحصول على المساهمات ذات القيم الموجبة فقط
  let filteredData = [];
  
  if (showBothAmounts) {
    // في حالة إجمالي المساهمات، نعرض جميع المساهمات التي لها قيمة موجبة في أي من الحقلين
    filteredData = window.reportData.filter(contribution => {
      return parseFloat(contribution.company_amount) > 0 || parseFloat(contribution.fund_amount) > 0;
    });
  } else {
    // في حالة مساهمات الشركة أو صندوق الزمالة، نعرض فقط المساهمات ذات القيم الموجبة في الحقل المحدد
    filteredData = window.reportData.filter(contribution => {
      return parseFloat(contribution[amountField]) > 0;
    });
  }
  
  if (filteredData.length === 0) {
    detailsTableBody.innerHTML = '';
    noDetailsMessage.style.display = 'block';
  } else {
    noDetailsMessage.style.display = 'none';
    
    // ترتيب البيانات حسب التاريخ (الأحدث أولاً)
    const sortedData = [...filteredData].sort((a, b) => {
      return new Date(b.contribution_date) - new Date(a.contribution_date);
    });
    
    // إنشاء صفوف الجدول
    detailsTableBody.innerHTML = '';
    
    // تحديث رؤوس الجدول إذا كنا نعرض إجمالي المساهمات
    const detailsTable = document.getElementById('detailsTable');
    const tableHeader = detailsTable.querySelector('thead tr');
    
    // إعادة تعيين رؤوس الجدول
    if (showBothAmounts) {
      tableHeader.innerHTML = `
        <th>اسم الموظف</th>
        <th>مساهمة الشركة</th>
        <th>مساهمة صندوق الزمالة</th>
        <th>إجمالي المساهمة</th>
        <th>نوع المساهمة</th>
        <th>التاريخ</th>
        <th>ملاحظات</th>
      `;
    } else {
      tableHeader.innerHTML = `
        <th>اسم الموظف</th>
        <th>قيمة المساهمة</th>
        <th>نوع المساهمة</th>
        <th>التاريخ</th>
        <th>ملاحظات</th>
      `;
    }
    
    sortedData.forEach(contribution => {
      const row = document.createElement('tr');
      
      // اسم الموظف
      const nameCell = document.createElement('td');
      nameCell.textContent = contribution.employee_name;
      row.appendChild(nameCell);
      
      if (showBothAmounts) {
        // مساهمة الشركة
        const companyAmountCell = document.createElement('td');
        companyAmountCell.className = 'company-amount';
        companyAmountCell.textContent = parseFloat(contribution.company_amount).toFixed(2) + ' جنيه';
        row.appendChild(companyAmountCell);

        // مساهمة صندوق الزمالة
        const fundAmountCell = document.createElement('td');
        fundAmountCell.className = 'fund-amount';
        fundAmountCell.textContent = parseFloat(contribution.fund_amount).toFixed(2) + ' جنيه';
        row.appendChild(fundAmountCell);
        
        // إجمالي المساهمة
        const totalAmountCell = document.createElement('td');
        const totalAmount = parseFloat(contribution.company_amount) + parseFloat(contribution.fund_amount);
        totalAmountCell.textContent = totalAmount.toFixed(2) + ' جنيه';
        row.appendChild(totalAmountCell);
      } else {
        // قيمة المساهمة
        const amountCell = document.createElement('td');
        amountCell.textContent = parseFloat(contribution[amountField]).toFixed(2) + ' جنيه';
        row.appendChild(amountCell);
      }
      
      // نوع المساهمة
      const contributionTypeCell = document.createElement('td');
      contributionTypeCell.textContent = getContributionTypeName(contribution.contribution_type);
      row.appendChild(contributionTypeCell);
      
      // التاريخ
      const dateCell = document.createElement('td');
      dateCell.textContent = formatDate(contribution.contribution_date);
      row.appendChild(dateCell);
      
      // ملاحظات
      const notesCell = document.createElement('td');
      notesCell.textContent = contribution.notes || '-';
      row.appendChild(notesCell);
      
      detailsTableBody.appendChild(row);
    });
  }
  
  // إظهار قسم التفاصيل
  detailsSection.style.display = 'block';
}

// تحميل المساهمات لعرضها في تبويب عرض المساهمات
function loadContributionsForView() {
  console.log('تحميل المساهمات لتبويب العرض');
  // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
}

// تهيئة تبويب التقارير
function initializeReportsTab() {
  console.log('تهيئة تبويب التقارير');
  // يمكن إضافة منطق إضافي هنا إذا لزم الأمر
}





// التحقق من وجود مساهمة مكررة لنفس الموظف ونفس النوع في نفس التاريخ
function checkDuplicateContribution(employeeCode, contributionType, contributionDate) {
  // البحث في المساهمات الموجودة
  const duplicate = contributions.find(contribution =>
    contribution.employee_code === employeeCode &&
    contribution.contribution_type === contributionType &&
    contribution.contribution_date === contributionDate
  );

  if (duplicate) {
    return {
      isDuplicate: true,
      contribution: duplicate
    };
  }

  return { isDuplicate: false };
}

// التحقق من وجود مساهمة مكررة للتحديث (باستثناء المساهمة الحالية)
function checkDuplicateContributionForUpdate(employeeCode, contributionType, contributionDate, currentContributionId) {
  // البحث في المساهمات الموجودة (باستثناء المساهمة الحالية)
  const duplicate = contributions.find(contribution =>
    contribution.employee_code === employeeCode &&
    contribution.contribution_type === contributionType &&
    contribution.contribution_date === contributionDate &&
    contribution.id != currentContributionId
  );

  if (duplicate) {
    return {
      isDuplicate: true,
      contribution: duplicate
    };
  }

  return { isDuplicate: false };
}

// الحصول على نص نوع المساهمة
function getContributionTypeText(contributionType) {
  const types = {
    'social_insurance': 'التأمين الاجتماعي',
    'medical_insurance': 'التأمين الطبي',
    'pension_fund': 'صندوق المعاشات',
    'savings_fund': 'صندوق الادخار',
    'union_subscription': 'اشتراك النقابة',
    'other': 'أخرى'
  };

  return types[contributionType] || contributionType;
}

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', initializePage);

