// متغيرات عامة
const API_URL = 'http://localhost:5500/api';
let custodyItems = [];
let deliveryRecords = [];

let employees = [];
let nextCustodyCode = 10000;
let nextOperationNumber = 1;
let userPermissions = {}; // متغير لتخزين صلاحيات المستخدم

// عناصر DOM
const tabBtns = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', async function() {
    // تحميل صلاحيات المستخدم أولاً
    await loadUserPermissions();

    await initializePage();
    setupEventListeners();
    setupTabSwitching();
    setupModalEventListeners();

    // إعداد قائمة اقتراحات للبحث في تسليم العهد
    setupDeliverySearch();

    // تعيين التاريخ الحالي
    setCurrentDate();

    // تطبيق الصلاحيات على الأزرار بعد تحميل البيانات وعرضها
    setupAllActionButtons();

    // التحقق من المحتوى المحدد من البطاقات بعد تحميل الصفحة
    setTimeout(() => {
        checkSelectedContent();
    }, 100);
});

// تهيئة الصفحة
async function initializePage() {
    try {
        await loadEmployees();
        await loadDepartments();
        await loadCustodyItems();
        await loadDeliveryRecords();


        // حساب الكميات المتاحة بعد تحميل جميع البيانات
        calculateAvailableQuantities();

        await generateNextCustodyCode();
        await generateNextOperationNumber();
        displayCustodyItems();
        displayDeliveryRecords();

        displayUndeliveredItems();
        loadAvailableCustodyForDelivery();
    } catch (error) {
        console.error('خطأ في تهيئة الصفحة:', error);
        alert('خطأ في تحميل البيانات');
    }
}

// تحميل الموظفين
async function loadEmployees() {
    try {
        // إضافة معامل لاستبعاد الموظفين المستقيلين
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/employees?include_resigned=false`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        if (response.ok) {
            employees = await response.json();

            // تحديث اقتراحات أسماء الموظفين
            updateEmployeeNameSuggestions();
        }
    } catch (error) {

    }
}

// تحديث اقتراحات أسماء الموظفين
function updateEmployeeNameSuggestions() {
    const datalist = document.getElementById('employeeNameSuggestions');
    if (!datalist) return;
    
    // مسح الاقتراحات الحالية
    datalist.innerHTML = '';
    
    // إضافة اقتراحات جديدة
    employees.forEach(employee => {
        const option = document.createElement('option');
        option.value = employee.full_name;
        option.setAttribute('data-code', employee.code);
        datalist.appendChild(option);
    });
}

// تحميل صلاحيات المستخدم - استخدام النظام الموحد
async function loadUserPermissions() {
    try {
        // استخدام النظام الموحد من permissions.js
        if (window.permissionManager) {
            // لا حاجة لتحميل منفصل، النظام الموحد يتولى ذلك
            console.log('تم استخدام النظام الموحد للصلاحيات');
        } else {
            // تحميل الصلاحيات من localStorage كنسخة احتياطية
            const permissionsStr = localStorage.getItem('permissions');
            if (permissionsStr) {
                userPermissions = JSON.parse(permissionsStr);
            } else {
                userPermissions = {
                    can_edit: false,
                    can_delete: false,
                    can_add: false
                };
            }
            console.log('تم تحميل صلاحيات المستخدم محلياً:', userPermissions);
        }
    } catch (error) {
        console.error('خطأ في تحميل صلاحيات المستخدم:', error);
        userPermissions = {
            can_edit: false,
            can_delete: false,
            can_add: false
        };
    }
}

// تطبيق الصلاحيات على جميع الأزرار
function setupAllActionButtons() {
    setupCustodyActionButtons();
    setupDeliveryActionButtons();
    setupReturnActionButtons();
}

// تطبيق الصلاحيات على أزرار جدول العهد
function setupCustodyActionButtons() {
    // انتظار قصير للتأكد من تحميل النظام الموحد
    setTimeout(() => {
        applyPermissionsToButtons('custodyTableBody');
        // استخدام النظام الموحد أيضاً
        if (window.permissionManager && typeof window.permissionManager.applyPermissionsToTable === 'function') {
            window.permissionManager.applyPermissionsToTable('custodyTableBody');
        }
    }, 50);
}

// تطبيق الصلاحيات على أزرار جدول التسليم
function setupDeliveryActionButtons() {
    setTimeout(() => {
        applyPermissionsToButtons('deliveryTableBody');
        // استخدام النظام الموحد أيضاً
        if (window.permissionManager && typeof window.permissionManager.applyPermissionsToTable === 'function') {
            window.permissionManager.applyPermissionsToTable('deliveryTableBody');
        }
    }, 50);
}

// تطبيق الصلاحيات على أزرار جدول الاسترجاع
function setupReturnActionButtons() {
    setTimeout(() => {
        applyPermissionsToButtons('returnTableBody');
        // استخدام النظام الموحد أيضاً
        if (window.permissionManager && typeof window.permissionManager.applyPermissionsToTable === 'function') {
            window.permissionManager.applyPermissionsToTable('returnTableBody');
        }
    }, 50);
}



// تطبيق الصلاحيات على الأزرار في جدول محدد
function applyPermissionsToButtons(tableBodyId) {
    const tableBody = document.getElementById(tableBodyId);
    if (!tableBody) return;

    // استثناء قسم إدارة المستخدمين من تطبيق الصلاحيات
    if (tableBodyId === 'usersTableBody') {
        return;
    }

    // البحث عن جميع الأزرار التي تحتاج إلى صلاحيات
    const buttons = tableBody.querySelectorAll('[data-permission]');

    buttons.forEach(button => {
        const permission = button.getAttribute('data-permission');

        if (permission) {
            // استخدام النظام الموحد للتحقق من الصلاحيات
            let hasPermissionValue = false;

            if (window.permissionManager && typeof window.permissionManager.hasPermission === 'function') {
                // استخدام النظام الموحد
                hasPermissionValue = window.permissionManager.hasPermission(permission);
            } else if (typeof hasPermission === 'function') {
                // استخدام الدالة المساعدة
                hasPermissionValue = hasPermission(permission);
            } else if (userPermissions.hasOwnProperty(permission)) {
                // استخدام النظام المحلي كنسخة احتياطية
                hasPermissionValue = userPermissions[permission];
            }

            // تطبيق الصلاحية
            if (!hasPermissionValue) {
                button.style.display = 'none';
            } else {
                button.style.display = 'inline-block';
            }
        }
    });
}

// تحميل الإدارات
function loadDepartments() {
    const departmentFilter = document.getElementById('departmentFilter');
    if (!departmentFilter) return; // إذا لم يوجد العنصر لا تفعل شيئًا
    const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))];
    departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';
    departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        departmentFilter.appendChild(option);
    });
}

// تحميل العهد
async function loadCustodyItems() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/custody`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        if (response.ok) {
            custodyItems = await response.json();
            console.log('تم تحميل العهد:', custodyItems.length);
        }
    } catch (error) {
        console.error('خطأ في تحميل العهد:', error);
        custodyItems = [];
    }
}

// حساب الكميات المتاحة لجميع العهد
function calculateAvailableQuantities() {
    custodyItems.forEach(item => {
        // حساب الكميات المسلمة حالياً (بحالة "مسلم")
        const currentlyDelivered = deliveryRecords
            .filter(rec => rec.custody_code == item.custody_code && rec.status === 'مسلم')
            .reduce((sum, rec) => sum + (parseInt(rec.quantity) || 0), 0);

        // الكمية المتاحة = الكمية الأصلية - الكميات المسلمة حالياً
        item.available_quantity = item.quantity - currentlyDelivered;

        // التأكد من أن الكمية المتاحة لا تقل عن الصفر
        if (item.available_quantity < 0) {
            item.available_quantity = 0;
        }
    });

    console.log('تم حساب الكميات المتاحة');
    console.log('العهد المتاحة للتسليم:', custodyItems.filter(item => item.available_quantity > 0).length);
    console.log('إجمالي العهد:', custodyItems.length);
}

// تحميل سجلات التسليم
async function loadDeliveryRecords() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/custody/delivery`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        if (response.ok) {
            deliveryRecords = await response.json();
            console.log('تم تحميل سجلات التسليم:', deliveryRecords.length);
            console.log('آخر سجل تسليم:', deliveryRecords[deliveryRecords.length - 1]);
        } else {
            console.error('خطأ في تحميل سجلات التسليم:', response.status);
            deliveryRecords = [];
        }
    } catch (error) {
        console.error('خطأ في تحميل سجلات التسليم:', error);
        deliveryRecords = [];
    }
}



// توليد كود العهد التالي
async function generateNextCustodyCode() {
    if (custodyItems.length > 0) {
        const maxCode = Math.max(...custodyItems.map(item => parseInt(item.custody_code)));
        nextCustodyCode = Math.max(maxCode + 1, 10000);
    } else {
        nextCustodyCode = 10000;
    }
    const custodyCodeField = document.getElementById('custodyCode');
    custodyCodeField.value = nextCustodyCode;
    custodyCodeField.readOnly = true;
}

// توليد رقم العملية التالي
async function generateNextOperationNumber() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/custody/delivery/operation-numbers`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        if (response.ok) {
            const operationNumbers = await response.json();
            if (operationNumbers.length > 0) {
                const maxOperation = Math.max(...operationNumbers.map(record => parseInt(record.operation_number)));
                nextOperationNumber = maxOperation + 1;
            }
        }
    } catch (error) {
        console.error('خطأ في جلب أرقام العمليات:', error);
    }
    document.getElementById('operationNumber').value = nextOperationNumber;
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // دالة مساعدة لإضافة مستمع حدث مع فحص وجود العنصر
    function addEventListenerSafe(elementId, event, handler) {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener(event, handler);
        } else {
            console.warn(`Element with ID '${elementId}' not found`);
        }
    }
    
    // نموذج إضافة عهدة
    addEventListenerSafe('addCustodyForm', 'submit', handleAddCustody);
    
    // نموذج تسليم عهدة
    addEventListenerSafe('deliverCustodyForm', 'submit', handleDeliverCustody);
    

    
    // ملء كود الموظف تلقائيًا عند اختيار اسم من القائمة
    addEventListenerSafe('employeeCodeDeliver', 'input', handleEmployeeNameSelection);
    addEventListenerSafe('employeeCodeDeliver', 'change', handleEmployeeNameSelection);
    
    // تغيير كود العهد في التسليم
    addEventListenerSafe('custodyCodeDeliver', 'change', updateCustodyDetails);
    
    // بحث في العهد
    addEventListenerSafe('searchCustody', 'input', function() {
        console.log('البحث في العهد:', this.value);
        filterCustodyItems();
    });
    
    // بحث مباشر في العهد للتسليم
    addEventListenerSafe('searchCustodyDeliver', 'input', function() {
        const searchTerm = this.value.trim();
        loadAvailableCustodyForDelivery(searchTerm);
    });
    
    // تم إزالة مستمع الحدث للعنصر غير الموجود 'searchCustodyBtn'
    
    const searchCustodyDeliver = document.getElementById('searchCustodyDeliver');
    if (searchCustodyDeliver) {
        searchCustodyDeliver.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchCustody();
            }
        });
        // إضافة مستمع حدث للتغيير في حقل البحث
        searchCustodyDeliver.addEventListener('input', function() {
            searchCustody();
        });
    }
    
    // بحث في التسليم
    addEventListenerSafe('searchDeliveryBtn', 'click', filterDeliveryRecords);
    

    
    // تحديث العهد غير المسلمة
    addEventListenerSafe('refreshUndeliveredBtn', 'click', displayUndeliveredItems);
    
    // تصدير البيانات
    addEventListenerSafe('exportCustodyBtn', 'click', () => exportToExcel('custody'));
    addEventListenerSafe('exportUndeliveredBtn', 'click', () => exportToExcel('undelivered'));
    addEventListenerSafe('exportDeliveryBtn', 'click', () => exportToExcel('delivery'));
    // addEventListenerSafe('exportReturnBtn', 'click', () => exportToExcel('return')); // تم تعطيله مؤقتاً
    
    // تقرير عهدة الموظف
    addEventListenerSafe('searchEmployeeReportBtn', 'click', searchEmployeeForReport);
    
    const employeeReportSearch = document.getElementById('employeeReportSearch');
    if (employeeReportSearch) {
        // مستمع حدث للبحث عند الضغط على Enter
        employeeReportSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchEmployeeForReport();
            }
        });
        
        // مستمع حدث للبحث المباشر عند كتابة الحروف
        employeeReportSearch.addEventListener('input', function() {
            searchEmployeeForReport();
        });
    }
    
    addEventListenerSafe('printEmployeeCustodyBtn', 'click', printEmployeeCustodyReport);
    addEventListenerSafe('exportEmployeeCustodyBtn', 'click', () => exportToExcel('employee-custody'));
}

// إعداد تبديل التبويبات
function setupTabSwitching() {
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetTab = btn.getAttribute('data-tab');
            
            // إزالة الفئة النشطة من جميع الأزرار والمحتويات
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(content => content.style.display = 'none');
            
            // إضافة الفئة النشطة للزر المحدد وإظهار المحتوى
            btn.classList.add('active');
            document.getElementById(targetTab).style.display = 'block';
            
            // تحديث البيانات حسب التبويب
            if (targetTab === 'deliver-custody') {
                loadAvailableCustodyForDelivery();
                displayDeliveryRecords(); // إضافة تحديث جدول التسليم
            } else if (targetTab === 'undelivered-custody') {
                displayUndeliveredItems();
            }
        });
    });
}

// وظيفة لعرض تبويب محدد برمجيًا
function showTab(tabId) {
    // تحديث الأزرار النشطة
    tabBtns.forEach(btn => {
        if (btn.getAttribute('data-tab') === tabId) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
    
    // تحديث محتوى التبويبات
    tabContents.forEach(content => {
        if (content.id === tabId) {
            content.style.display = 'block';
            // تحديث البيانات حسب التبويب
            if (tabId === 'deliver-custody') {
                loadAvailableCustodyForDelivery();
                displayDeliveryRecords(); // إضافة تحديث جدول التسليم
            } else if (tabId === 'undelivered-custody') {
                displayUndeliveredItems();
            }
        } else {
            content.style.display = 'none';
        }
    });
}

// معالجة إضافة عهدة
async function handleAddCustody(event) {
    event.preventDefault();
    
    const formData = {
        custody_code: document.getElementById('custodyCode').value,
        name: document.getElementById('custodyName').value,
        type: document.getElementById('custodyType').value,
        status: document.getElementById('custodyStatus').value,
        quantity: parseInt(document.getElementById('custodyQuantity').value),
        available_quantity: parseInt(document.getElementById('custodyQuantity').value)
    };
    
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/custody`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(formData)
        });
        
        if (response.ok) {
            showCustodyMessage('تم إضافة العهدة بنجاح', 'success');
            document.getElementById('addCustodyForm').reset();
            await loadCustodyItems();
            calculateAvailableQuantities();
            await generateNextCustodyCode();
            displayCustodyItems();
            displayUndeliveredItems();
            loadAvailableCustodyForDelivery();
        } else if (response.status === 409) {
            // حالة تكرار الاسم
            console.log('حالة تكرار - الكود 409');
            const error = await response.json();
            console.log('بيانات الخطأ:', error);
            if (error.duplicate_name) {
                console.log('تكرار اسم العهدة');
                showCustodyMessage(`تنبيه: ${error.error}`, 'error');
                // تركيز على حقل اسم العهدة
                document.getElementById('custodyName').focus();
                document.getElementById('custodyName').select();
            } else {
                console.log('خطأ آخر في الكود 409');
                showCustodyMessage(error.error || 'خطأ في إضافة العهدة', 'error');
            }
        } else {
            console.log('خطأ آخر:', response.status);
            const error = await response.json();
            console.log('بيانات الخطأ:', error);
            showCustodyMessage(error.error || 'خطأ في إضافة العهدة', 'error');
        }
    } catch (error) {
        console.error('خطأ في إضافة العهدة:', error);
        showCustodyMessage('خطأ في الاتصال بالخادم', 'error');
    }
}

// دالة لعرض رسائل العهد
function showCustodyMessage(message, type) {
    console.log('عرض رسالة العهد:', message, type);

    // إزالة أي رسائل سابقة
    const existingMessages = document.querySelectorAll('.custody-message');
    existingMessages.forEach(msg => {
        if (msg.parentNode) {
            msg.parentNode.removeChild(msg);
        }
    });

    const messageDiv = document.createElement('div');
    messageDiv.className = `custody-message ${type}-message`;
    messageDiv.innerHTML = `
        <div class="message-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'}"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(messageDiv);
    console.log('تم إضافة الرسالة للصفحة');

    // إزالة الرسالة بعد 4 ثوان
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
            console.log('تم إزالة الرسالة');
        }
    }, 4000);
}

// معالجة تسليم عهدة
async function handleDeliverCustody(event) {
    event.preventDefault();
    
    // الحصول على اسم العهدة من الحقل المخفي
    const custodyCode = document.getElementById('custodyCodeDeliver').value;
    const custodyName = document.getElementById('custodyNameDeliver').value;
    
    const formData = {
        operation_number: document.getElementById('operationNumber').value,
        employee_code: document.getElementById('employeeCodeDeliver').value,
        department: document.getElementById('employeeDepartment').value,
        employee_name: document.getElementById('employeeName').value,
        custody_code: custodyCode,
        custody_name: custodyName,
        custody_type: document.getElementById('custodyTypeDeliver').value,
        quantity: parseInt(document.getElementById('deliveryQuantity').value),
        delivery_date: document.getElementById('deliveryDate').value,
        status: document.getElementById('deliveryStatus').value
    };

    // تسجيل البيانات المرسلة للتشخيص
    console.log('بيانات التسليم المرسلة:', formData);

    // تسجيل قيم الحقول الفردية للتشخيص
    console.log('قيم الحقول:');
    console.log('- رقم العملية:', document.getElementById('operationNumber').value);
    console.log('- كود الموظف:', document.getElementById('employeeCodeDeliver').value);
    console.log('- اسم الموظف:', document.getElementById('employeeName').value);
    console.log('- الإدارة:', document.getElementById('employeeDepartment').value);
    console.log('- كود العهدة:', custodyCode);
    console.log('- اسم العهدة:', custodyName);
    console.log('- نوع العهدة:', document.getElementById('custodyTypeDeliver').value);
    console.log('- الكمية:', document.getElementById('deliveryQuantity').value);
    console.log('- التاريخ:', document.getElementById('deliveryDate').value);
    console.log('- الحالة:', document.getElementById('deliveryStatus').value);

    // التحقق من صحة البيانات قبل الإرسال
    if (!formData.operation_number) {
        alert('رقم العملية مطلوب');
        return;
    }
    if (!formData.employee_code) {
        alert('كود الموظف مطلوب');
        return;
    }
    if (!formData.employee_name) {
        alert('اسم الموظف مطلوب');
        return;
    }
    if (!formData.custody_code) {
        alert('كود العهدة مطلوب');
        return;
    }
    if (!formData.custody_name) {
        alert('اسم العهدة مطلوب');
        return;
    }
    if (!formData.custody_type) {
        alert('نوع العهدة مطلوب');
        return;
    }
    if (!formData.quantity || formData.quantity <= 0) {
        alert('الكمية يجب أن تكون أكبر من صفر');
        return;
    }
    if (!formData.delivery_date) {
        alert('تاريخ التسليم مطلوب');
        return;
    }

    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/custody/delivery`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(formData)
        });
        
        if (response.ok) {
            alert('تم تسليم العهدة بنجاح');
            document.getElementById('deliverCustodyForm').reset();

            // إعادة تحميل البيانات وتحديث الجداول
            await loadDeliveryRecords();
            await loadCustodyItems();
            calculateAvailableQuantities();
            await generateNextOperationNumber();

            // تحديث الجداول مع تأخير قصير لضمان التحديث الصحيح
            setTimeout(() => {
                displayDeliveryRecords();
                displayUndeliveredItems();
                loadAvailableCustodyForDelivery();
                console.log('تم تحديث جميع الجداول بعد التسليم');
            }, 100);
        } else {
            const error = await response.json();
            console.error('خطأ من الخادم:', error);
            console.error('حالة الاستجابة:', response.status);
            alert(error.error || 'خطأ في تسليم العهدة');
        }
    } catch (error) {
        console.error('خطأ في تسليم العهدة:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}



// بحث عن موظف
function searchEmployee() {
    const searchValue = normalizeArabic(document.getElementById('employeeCodeDeliver').value.trim());
    const employee = employees.find(emp =>
        normalizeArabic(emp.code) == searchValue ||
        normalizeArabic(emp.full_name).includes(searchValue)
    );

    if (employee) {
        document.getElementById('employeeCodeDeliver').value = employee.code;
        document.getElementById('employeeDepartment').value = employee.department || '';
        document.getElementById('employeeName').value = employee.full_name || '';
        console.log('تم العثور على الموظف');
    } else {
        document.getElementById('employeeName').value = '';
        alert('لم يتم العثور على الموظف');
    }
}

// إعداد قائمة اقتراحات للبحث في تسليم العهد
function setupDeliverySearch() {
    const employeeCodeDeliver = document.getElementById('employeeCodeDeliver');
    const deliverySuggestions = document.getElementById('employee-delivery-suggestions');

    if (employeeCodeDeliver && deliverySuggestions) {
        employeeCodeDeliver.addEventListener('input', function() {
            const searchValue = normalizeArabic(this.value.trim().toLowerCase());

            if (searchValue.length < 2) {
                deliverySuggestions.style.display = 'none';
                return;
            }

            const filteredEmployees = employees.filter(emp =>
                normalizeArabic(emp.full_name.toLowerCase()).includes(searchValue) ||
                emp.code.toString().includes(searchValue)
            ).slice(0, 5);

            if (filteredEmployees.length > 0) {
                deliverySuggestions.innerHTML = filteredEmployees.map(emp =>
                    `<div class="suggestion-item" data-code="${emp.code}" data-name="${emp.full_name}">
                        <strong>${emp.code}</strong> - ${emp.full_name}
                    </div>`
                ).join('');
                deliverySuggestions.style.display = 'block';

                // إضافة مستمعي الأحداث للاقتراحات
                deliverySuggestions.querySelectorAll('.suggestion-item').forEach(item => {
                    item.addEventListener('click', function() {
                        const code = this.getAttribute('data-code');
                        const name = this.getAttribute('data-name');
                        employeeCodeDeliver.value = code;
                        document.getElementById('employeeName').value = name;

                        // البحث عن الموظف وملء باقي البيانات
                        const employee = employees.find(emp => emp.code == code);
                        if (employee) {
                            document.getElementById('employeeDepartment').value = employee.department || '';
                        }

                        deliverySuggestions.style.display = 'none';
                    });
                });
            } else {
                deliverySuggestions.style.display = 'none';
            }
        });

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!employeeCodeDeliver.contains(e.target) && !deliverySuggestions.contains(e.target)) {
                deliverySuggestions.style.display = 'none';
            }
        });
    }
}

// التعامل مع اختيار اسم الموظف من قائمة الاقتراحات
function handleEmployeeNameSelection() {
    const employeeCodeInput = document.getElementById('employeeCodeDeliver');
    const employeeNameInput = document.getElementById('employeeName');
    const employeeDepartmentInput = document.getElementById('employeeDepartment');
    const selectedValue = employeeCodeInput.value.trim();
    
    if (!selectedValue) {
        employeeNameInput.value = '';
        employeeDepartmentInput.value = '';
        return;
    }
    
    // البحث عن الموظف بالاسم الكامل
    const employee = employees.find(emp => 
        emp.full_name === selectedValue || emp.code === selectedValue
    );
    
    if (employee) {
        // إذا تم العثور على الموظف، املأ الحقول
        employeeCodeInput.value = employee.code;
        employeeNameInput.value = employee.full_name || '';
        employeeDepartmentInput.value = employee.department || '';
    } else {
        // البحث في خيارات datalist
        const datalist = document.getElementById('employeeNameSuggestions');
        if (datalist) {
            const options = datalist.querySelectorAll('option');
            for (const option of options) {
                if (option.value === selectedValue) {
                    const code = option.getAttribute('data-code');
                    if (code) {
                        // البحث عن الموظف بالكود
                        const employeeByCode = employees.find(emp => emp.code === code);
                        if (employeeByCode) {
                            employeeCodeInput.value = employeeByCode.code;
                            employeeNameInput.value = employeeByCode.full_name || '';
                            employeeDepartmentInput.value = employeeByCode.department || '';
                            return;
                        }
                    }
                }
            }
        }
    }
}

// دالة لتوحيد الحروف العربية وإزالة الهمزات والتاء المربوطة
function normalizeArabic(str) {
    if (!str) return '';
    return str
        .replace(/[أإآا]/g, 'ا')
        .replace(/[ة]/g, 'ه')
        .replace(/[ى]/g, 'ي')
        .replace(/[ؤئ]/g, 'و')
        .replace(/[ء]/g, '')
        .replace(/\s+/g, '')
        .toLowerCase();
}

// عرض اقتراحات الموظفين أثناء الكتابة
document.getElementById('employeeCodeDeliver').addEventListener('input', function() {
    const searchValue = normalizeArabic(this.value.trim());
    if (searchValue.length < 2) return;
    
    const suggestions = employees.filter(emp => 
        normalizeArabic(emp.code.toString()).includes(searchValue) || 
        normalizeArabic(emp.full_name).includes(searchValue)
    ).slice(0, 5);
    
    // إنشاء قائمة الاقتراحات
    let suggestionList = document.getElementById('employee-suggestions');
    if (!suggestionList) {
        suggestionList = document.createElement('div');
        suggestionList.id = 'employee-suggestions';
        suggestionList.className = 'suggestions-list';
        this.parentNode.appendChild(suggestionList);
    }
    
    // إضافة الاقتراحات للقائمة
    if (suggestions.length > 0) {
        suggestionList.innerHTML = '';
        suggestions.forEach(emp => {
            const item = document.createElement('div');
            item.className = 'suggestion-item';
            item.textContent = `${emp.code} - ${emp.full_name}`;
            item.addEventListener('click', function() {
                document.getElementById('employeeCodeDeliver').value = emp.code;
                document.getElementById('employeeDepartment').value = emp.department || '';
                document.getElementById('employeeName').value = emp.full_name || '';
                suggestionList.innerHTML = '';
                suggestionList.style.display = 'none';
            });
            suggestionList.appendChild(item);
        });
        suggestionList.style.display = 'block';
    } else {
        suggestionList.innerHTML = '';
        suggestionList.style.display = 'none';
    }
});

// إخفاء قائمة الاقتراحات عند النقر خارجها
document.addEventListener('click', function(e) {
    const suggestionList = document.getElementById('employee-suggestions');
    if (suggestionList && !e.target.matches('#employeeCodeDeliver')) {
        suggestionList.innerHTML = '';
        suggestionList.style.display = 'none';
    }
});







// تحديث تفاصيل العهدة
function updateCustodyDetails() {
    const custodySelect = document.getElementById('custodyCodeDeliver');
    const custodyCode = custodySelect.value;

    if (custodyCode) {
        // البحث عن العهدة في المصفوفة للحصول على جميع البيانات
        const custody = custodyItems.find(item => item.custody_code == custodyCode);

        if (custody) {
            // استخدام البيانات من المصفوفة مباشرة
            const custodyName = custody.custody_name || custody.name || '';
            const custodyType = custody.custody_type || custody.type || '';

            document.getElementById('custodyTypeDeliver').value = custodyType;
            document.getElementById('custodyNameDeliver').value = custodyName;
            document.getElementById('deliveryQuantity').max = custody.available_quantity;
            document.getElementById('deliveryQuantity').value = Math.min(1, custody.available_quantity);

            console.log('تم تحديث تفاصيل العهدة:', {
                code: custodyCode,
                name: custodyName,
                type: custodyType,
                available: custody.available_quantity
            });

            // تأكيد أن القيم تم تعيينها بشكل صحيح
            console.log('قيم الحقول بعد التحديث:');
            console.log('- نوع العهدة في الحقل:', document.getElementById('custodyTypeDeliver').value);
            console.log('- اسم العهدة في الحقل:', document.getElementById('custodyNameDeliver').value);
        }
    } else {
        // مسح القيم عند عدم اختيار عهدة
        document.getElementById('custodyTypeDeliver').value = '';
        document.getElementById('custodyNameDeliver').value = '';
        document.getElementById('deliveryQuantity').max = '';
        document.getElementById('deliveryQuantity').value = '';

        console.log('تم مسح تفاصيل العهدة');
    }
}

// تحميل العهد المتاحة للتسليم
function loadAvailableCustodyForDelivery(searchTerm = '') {
    const custodySelect = document.getElementById('custodyCodeDeliver');
    custodySelect.innerHTML = '<option value="">اختر العهدة</option>';
    
    let availableCustody = custodyItems.filter(item => item.available_quantity > 0);
    
    // تصفية العهد بناءً على مصطلح البحث إذا كان موجودًا
    if (searchTerm) {
        const normalizedSearchTerm = normalizeArabic(searchTerm);
        availableCustody = availableCustody.filter(custody => {
            const custodyName = custody.custody_name || custody.name || '';
            const custodyType = custody.custody_type || custody.type || '';

            return normalizeArabic(custodyName).includes(normalizedSearchTerm) ||
                   custody.custody_code.toString().includes(normalizedSearchTerm) ||
                   normalizeArabic(custodyType).includes(normalizedSearchTerm);
        });
    }
    
    availableCustody.forEach(custody => {
        const option = document.createElement('option');
        option.value = custody.custody_code;

        // استخدام الأسماء الصحيحة للحقول
        const custodyName = custody.custody_name || custody.name || '';
        const custodyType = custody.custody_type || custody.type || '';

        option.setAttribute('data-custody-name', custodyName);
        option.setAttribute('data-custody-type', custodyType);
        option.textContent = `${custodyName} - ${custodyType} (متاح: ${custody.available_quantity})`;
        custodySelect.appendChild(option);
    });
}

// تطبيع الحروف العربية للبحث
function normalizeArabic(text) {
    if (!text) return '';
    return text
        .replace(/[يى]/g, 'ي')
        .replace(/[ةه]/g, 'ه')
        .replace(/أ|إ|آ/g, 'ا')
        .toLowerCase();
}

// البحث عن الموظف لعرض تقرير العهدة
function searchEmployeeForReport() {
    const searchTerm = document.getElementById('employeeReportSearch').value.trim();
    if (!searchTerm) {
        // إخفاء معلومات الموظف والجدول عند مسح حقل البحث
        document.getElementById('employeeInfoContainer').style.display = 'none';
        document.getElementById('employeeCustodyTableBody').innerHTML = '';
        return;
    }
    
    const normalizedSearchTerm = normalizeArabic(searchTerm.toLowerCase());
    
    // البحث عن الموظف
    const employee = employees.find(emp => 
        normalizeArabic(emp.full_name ? emp.full_name.toLowerCase() : '').includes(normalizedSearchTerm) || 
        emp.code.toString().includes(searchTerm)
    );
    
    if (!employee) {
        // إخفاء معلومات الموظف والجدول عند عدم العثور على الموظف
        document.getElementById('employeeInfoContainer').style.display = 'none';
        document.getElementById('employeeCustodyTableBody').innerHTML = '';
        return;
    }
    
    // عرض معلومات الموظف
    document.getElementById('reportEmployeeCode').textContent = employee.code;
    document.getElementById('reportEmployeeName').textContent = employee.full_name || 'غير محدد';
    document.getElementById('reportEmployeeDepartment').textContent = employee.department || 'غير محدد';
    
    // إظهار حاوية معلومات الموظف
    document.getElementById('employeeInfoContainer').style.display = 'block';
    
    // البحث عن عهد الموظف
    const employeeDeliveries = deliveryRecords.filter(record => 
        record.employee_code.toString() === employee.code.toString()
    );
    
    if (employeeDeliveries.length === 0) {
        document.getElementById('employeeCustodyTableBody').innerHTML =
            '<tr><td colspan="8" class="no-data">لا توجد عهد مسجلة</td></tr>';
        return;
    }
    
    // عرض عهد الموظف
    const tbody = document.getElementById('employeeCustodyTableBody');
    tbody.innerHTML = '';
    
    employeeDeliveries.forEach(delivery => {
        // البحث عن معلومات العهدة
        const custodyItem = custodyItems.find(item => item.custody_code === delivery.custody_code);
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${delivery.custody_code || 'غير محدد'}</td>
            <td>${custodyItem ? custodyItem.name : delivery.custody_name || 'غير محدد'}</td>
            <td>${delivery.custody_type || 'غير محدد'}</td>
            <td>${new Date(delivery.delivery_date).toLocaleDateString('ar-EG')}</td>
            <td><strong>${delivery.quantity}</strong></td>
        `;
        tbody.appendChild(row);
    });
    
    // تطبيق الصلاحيات على أزرار الجدول
    setupDeliveryActionButtons();
    
    // عرض قسم التقرير
    document.getElementById('employee-custody-report').style.display = 'block';
    document.querySelector('[data-tab="employee-custody-report"]').classList.add('active');
}

// طباعة تقرير عهدة الموظف
function printEmployeeCustodyReport() {
    const employeeCode = document.getElementById('employeeReportCode').textContent;
    const employeeName = document.getElementById('employeeReportName').textContent;
    const employeeDepartment = document.getElementById('employeeReportDepartment').textContent;
    
    if (!employeeCode || !employeeName) {
        alert('يرجى البحث عن موظف أولاً');
        return;
    }
    
    const custodyTable = document.getElementById('employeeCustodyTable');
    if (!custodyTable) return;
    
    const printWindow = window.open('', '', 'width=800,height=600');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>تقرير عهدة الموظف - ${employeeName}</title>
            <style>
                @page { size: A4; margin: 1cm; }
                body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                h1 { text-align: center; color: #333; margin-bottom: 20px; }
                h3 { margin-top: 20px; color: #555; border-bottom: 2px solid #2196F3; padding-bottom: 5px; }
                .info-container { display: flex; justify-content: space-between; margin-bottom: 20px; background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
                .info-item { padding: 10px; }
                .info-label { font-weight: bold; color: #555; }
                table { width: 100%; border-collapse: collapse; margin-top: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                th, td { border: 1px solid #ddd; padding: 10px; text-align: right; }
                th { background-color: #2196F3; color: white; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .status-delivered { color: #2196F3; font-weight: bold; }
                .status-returned { color: #4CAF50; font-weight: bold; }
                .status-partial { color: #FF9800; font-weight: bold; }
                .btn { display: inline-block; padding: 8px 20px; border-radius: 4px; cursor: pointer; font-weight: bold; text-align: center; margin: 0 10px; min-width: 100px; border: none; }
                .print-btn { background-color: #2196F3; color: white; }
                .close-btn { background-color: #f44336; color: white; }
                @media print { 
                    .no-print { display: none; } 
                    body { margin: 0; padding: 10px; }
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; page-break-after: auto; }
                    h1 { margin-top: 0; }
                }
            </style>
        </head>
        <body>
            <h1>تقرير عهدة الموظف</h1>
            
            <div class="info-container">
                <div class="info-item"><span class="info-label">كود الموظف:</span> ${employeeCode}</div>
                <div class="info-item"><span class="info-label">اسم الموظف:</span> ${employeeName}</div>
                <div class="info-item"><span class="info-label">القسم:</span> ${employeeDepartment}</div>
            </div>
            
            <h3>قائمة العهد المستلمة</h3>
            ${custodyTable.outerHTML}
            
            <div class="no-print" style="margin-top: 30px; text-align: center;">
                <button onclick="window.print()" class="btn print-btn">طباعة</button>
                <button onclick="window.close()" class="btn close-btn">إغلاق</button>
            </div>
            
            <script>
                window.onload = function() {
                    setTimeout(function() {
                        window.print();
                    }, 500);
                }
            </script>
        </body>
        </html>
    `);
    printWindow.document.close();
}

// وظيفة البحث في العهد للتسليم
function searchCustody() {
    const searchTerm = document.getElementById('searchCustodyDeliver').value.trim();
    loadAvailableCustodyForDelivery(searchTerm);
}

// عرض العهد
function displayCustodyItems() {
    const tbody = document.getElementById('custodyTableBody');
    tbody.innerHTML = '';

    // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
    const sortedItems = [...custodyItems].sort((a, b) => {
      // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
      const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
      const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
      return bTime - aTime; // الأحدث أولاً
    });

    sortedItems.forEach((item, index) => {
        // حساب الكمية المصروفة بشكل صحيح
        // نحسب الكميات المسلمة حاليًا (بحالة "مسلم")
        const currentlyDelivered = deliveryRecords
            .filter(rec => rec.custody_code == item.custody_code && rec.status === 'مسلم')
            .reduce((sum, rec) => sum + (parseInt(rec.quantity) || 0), 0);
        
        // لا توجد كميات مسترجعة بعد حذف قسم الاسترجاع
        const returned = 0;
        
        // الكمية المصروفة = الكميات المسلمة حاليًا
        // لأن سجلات التسليم بحالة "مسلم" تعكس بالفعل الكميات المتبقية بعد الاسترجاع
        const spentQuantity = currentlyDelivered;
        const remainingQuantity = item.quantity - spentQuantity;
        
        const row = document.createElement('tr');

        // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
        if (index === 0) {
          row.style.backgroundColor = '#e8f5e8';
          row.style.border = '2px solid #4CAF50';
        }

        row.innerHTML = `
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.custody_code}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.name}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.type}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.status}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.quantity}</td>
            <td class="quantity-spent" style="${index === 0 ? 'font-weight: bold;' : ''}">${spentQuantity}</td>
            <td class="quantity-remaining" style="${index === 0 ? 'font-weight: bold;' : ''}">${remainingQuantity}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${item.created_at ? new Date(item.created_at).toLocaleDateString('ar-EG') : ''}</td>
            <td>
                <button class="action-btn details-btn" onclick="showCustodyDetails(${item.id})">تفاصيل المستلم</button>
                ${hasPermission('edit_custody') ? `<button class="action-btn edit-btn" onclick="editCustody(${item.id})">تعديل</button>` : ''}
                ${hasPermission('delete_custody') ? `<button class="action-btn delete-btn" onclick="deleteCustody(${item.id})">حذف</button>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
    
    // الأزرار الآن تُخفى مباشرة في HTML
 }

// عرض سجلات التسليم
function displayDeliveryRecords() {
    const tbody = document.getElementById('deliveryTableBody');
    if (!tbody) {
        console.error('لم يتم العثور على جدول التسليم');
        return;
    }

    tbody.innerHTML = '';
    console.log('عرض سجلات التسليم:', deliveryRecords.length, 'سجل');

    if (deliveryRecords.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="11" class="no-results">لا توجد سجلات تسليم</td>`;
        tbody.appendChild(row);
        return;
    }

    // ترتيب البيانات حسب تاريخ الإنشاء (الأحدث أولاً)
    const sortedRecords = [...deliveryRecords].sort((a, b) => {
      // ترتيب حسب created_at إذا كان متوفراً، وإلا حسب id
      const aTime = a.created_at ? new Date(a.created_at).getTime() : (a.id || 0);
      const bTime = b.created_at ? new Date(b.created_at).getTime() : (b.id || 0);
      return bTime - aTime; // الأحدث أولاً
    });

    sortedRecords.forEach((record, index) => {
        const row = document.createElement('tr');

        // تمييز الصف الأول (آخر إضافة) بلون أخضر فاتح
        if (index === 0) {
          row.style.backgroundColor = '#e8f5e8';
          row.style.border = '2px solid #4CAF50';
        }

        row.innerHTML = `
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.operation_number}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.employee_code}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.employee_name}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.department}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.custody_code}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.custody_name || getCustodyName(record.custody_code)}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.custody_type}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${record.quantity}</td>
            <td style="${index === 0 ? 'font-weight: bold;' : ''}">${new Date(record.delivery_date).toLocaleDateString('ar-EG')}</td>
            <td><span class="status-${record.status === 'مسلم' ? 'delivered' : 'pending'}" style="${index === 0 ? 'font-weight: bold;' : ''}">${record.status}</span></td>
            <td>
                ${hasPermission('edit_deliver_custody') ? `<button class="action-btn edit-btn" onclick="editDelivery(${record.id})">تعديل</button>` : ''}
                ${hasPermission('delete_deliver_custody') ? `<button class="action-btn delete-btn" onclick="deleteDelivery(${record.id})">حذف</button>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
    
    // تطبيق الصلاحيات على أزرار الجدول
    setupDeliveryActionButtons();
}

// الحصول على اسم العهدة من كود العهدة
function getCustodyName(custodyCode) {
    const custody = custodyItems.find(item => item.custody_code == custodyCode);
    return custody ? custody.name : '-';
}

// عرض العهد غير المسلمة
function displayUndeliveredItems() {
    const tbody = document.getElementById('undeliveredTableBody');
    tbody.innerHTML = '';
    
    const undeliveredItems = custodyItems.filter(item => item.available_quantity > 0);
    
    // تحديث عدد العهد غير المسلمة
    updateCustodyCount(undeliveredItems.length);
    
    // تطبيق التصفية إذا كانت موجودة
    const filteredItems = filterUndeliveredItems(undeliveredItems);
    
    if (filteredItems.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `<td colspan="6" class="no-results">لا توجد عهد غير مسلمة</td>`;
        tbody.appendChild(row);
        return;
    }
    
    filteredItems.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.custody_code}</td>
            <td>${item.name}</td>
            <td>${item.type}</td>
            <td>${item.status}</td>
            <td>${item.available_quantity}</td>
            <td>${item.created_at ? new Date(item.created_at).toLocaleDateString('ar-EG') : ''}</td>
        `;
        tbody.appendChild(row);
    });
}

// تصفية العهد غير المسلمة
function filterUndeliveredItems(items) {
    const searchTerm = document.getElementById('searchUndeliveredAll')?.value.toLowerCase() || '';
    
    // إذا لم تكن هناك معايير بحث، أعد جميع العناصر
    if (!searchTerm) {
        return items;
    }
    
    return items.filter(item => {
        // البحث بكود العهدة
        const codeMatch = item.custody_code.toString().includes(searchTerm);
        
        // البحث باسم العهدة
        const nameMatch = item.name.toLowerCase().includes(searchTerm);
        
        // البحث بنوع العهدة
        const typeMatch = item.type.toLowerCase().includes(searchTerm);
        
        // إذا تطابق أي من المعايير، أعد العنصر
        return codeMatch || nameMatch || typeMatch;
    });
}

// تحديث عدد العهد
function updateCustodyCount(count) {
    const title = document.querySelector('#undelivered-custody h2');
    if (title) {
        title.textContent = `العهد غير المسلمة (${count})`;
    }
}



// البحث في العهد من السيرفر
async function searchCustodyFromServer() {
    try {
        const searchInput = document.getElementById('searchCustody');
        if (!searchInput) {
            console.error('حقل البحث غير موجود');
            return;
        }

        const searchTerm = searchInput.value.trim();

        const params = new URLSearchParams();
        if (searchTerm) {
            params.append('name', searchTerm);
            params.append('type', searchTerm);
            params.append('custody_code', searchTerm);
        }

        let url = `${API_URL}/api/custody`;
        if (params.toString()) {
            url = `${API_URL}/api/custody/search?${params.toString()}`;
        }

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('فشل في جلب بيانات العهد');
        }

        const filteredItems = await response.json();
        custodyItems = filteredItems;
        displayCustodyItems();

    } catch (error) {
        console.error('خطأ في البحث:', error);
        showNotification('خطأ في البحث عن العهد', 'error');
    }
}

// تصفية العهد (استخدام البحث من السيرفر)
function filterCustodyItems() {
    searchCustodyFromServer();
}

    const normalizedSearchTerm = normalizeArabic(searchTerm.toLowerCase());
    const filteredItems = custodyItems.filter(item => {
        const normalizedName = normalizeArabic(item.name.toLowerCase());
        const normalizedType = normalizeArabic(item.type.toLowerCase());
        const custodyCode = item.custody_code.toString();

        return normalizedName.includes(normalizedSearchTerm) ||
               normalizedType.includes(normalizedSearchTerm) ||
               custodyCode.includes(searchTerm) ||
               item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
               item.type.toLowerCase().includes(searchTerm.toLowerCase());
    });

    const tbody = document.getElementById('custodyTableBody');
    tbody.innerHTML = '';

    filteredItems.forEach(item => {
        // حساب الكمية المصروفة بشكل صحيح
        // نحسب الكميات المسلمة حاليًا (بحالة "مسلم")
        const currentlyDelivered = deliveryRecords
            .filter(rec => rec.custody_code == item.custody_code && rec.status === 'مسلم')
            .reduce((sum, rec) => sum + (parseInt(rec.quantity) || 0), 0);

        // لا توجد كميات مسترجعة بعد حذف قسم الاسترجاع
        const returned = 0;

        // الكمية المصروفة = الكميات المسلمة حاليًا
        const spentQuantity = currentlyDelivered;
        const remainingQuantity = item.quantity - spentQuantity;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.custody_code}</td>
            <td>${item.name}</td>
            <td>${item.type}</td>
            <td>${item.status}</td>
            <td>${item.quantity}</td>
            <td class="quantity-spent">${spentQuantity}</td>
            <td class="quantity-remaining">${remainingQuantity}</td>
            <td>${item.created_at ? new Date(item.created_at).toLocaleDateString('ar-EG') : ''}</td>
            <td>
                <button class="action-btn details-btn" onclick="showCustodyDetails(${item.id})">تفاصيل المستلم</button>
                ${hasPermission('edit_custody') ? `<button class="action-btn edit-btn" onclick="editCustody(${item.id})">تعديل</button>` : ''}
                ${hasPermission('delete_custody') ? `<button class="action-btn delete-btn" onclick="deleteCustody(${item.id})">حذف</button>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });

    // إظهار رسالة إذا لم يتم العثور على نتائج
    if (filteredItems.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" class="no-data" style="text-align: center; padding: 20px; color: #666;">لا توجد نتائج مطابقة للبحث</td></tr>';
    }

    console.log(`تم العثور على ${filteredItems.length} نتيجة من أصل ${custodyItems.length} عنصر`);
}

// البحث في سجلات التسليم من السيرفر
async function searchDeliveryFromServer() {
    try {
        const searchTerm = document.getElementById('searchDelivery').value.toLowerCase();

        const params = new URLSearchParams();
        if (searchTerm) {
            params.append('employee_name', searchTerm);
            params.append('custody_type', searchTerm);
            params.append('employee_code', searchTerm);
        }

        let url = `${API_URL}/api/custody/delivery`;
        if (params.toString()) {
            url = `${API_URL}/api/custody/delivery/search?${params.toString()}`;
        }

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('فشل في جلب بيانات سجلات التسليم');
        }

        const filteredRecords = await response.json();
        deliveryRecords = filteredRecords;
        displayDeliveryRecords(filteredRecords);

    } catch (error) {
        console.error('خطأ في البحث:', error);
        showNotification('خطأ في البحث عن سجلات التسليم', 'error');
    }
}

// تصفية سجلات التسليم (استخدام البحث من السيرفر)
function filterDeliveryRecords() {
    searchDeliveryFromServer();
}

// عرض سجلات التسليم
function displayDeliveryRecords(records) {
    const tbody = document.getElementById('deliveryTableBody');
    tbody.innerHTML = '';

    records.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${record.operation_number}</td>
            <td>${record.employee_code}</td>
            <td>${record.employee_name}</td>
            <td>${record.department}</td>
            <td>${record.custody_code}</td>
            <td>${record.custody_name || getCustodyName(record.custody_code)}</td>
            <td>${record.custody_type}</td>
            <td>${record.quantity}</td>
            <td>${new Date(record.delivery_date).toLocaleDateString('ar-EG')}</td>
            <td><span class="status-${record.status === 'مسلم' ? 'delivered' : 'pending'}">${record.status}</span></td>
            <td>
                <button class="action-btn edit-btn" data-permission="can_edit" onclick="editDelivery(${record.id})">تعديل</button>
                <button class="action-btn delete-btn" data-permission="can_delete" onclick="deleteDelivery(${record.id})">حذف</button>
            </td>
        `;
        tbody.appendChild(row);
    });

    // تطبيق الصلاحيات على أزرار الجدول
    setupDeliveryActionButtons();
}

// إعداد البحث في العهد غير المسلمة
function setupUndeliveredSearch() {
    // إضافة مستمع الأحداث لحقل البحث الموحد
    const searchField = document.getElementById('searchUndeliveredAll');
    if (searchField) {
        searchField.addEventListener('input', () => {
            displayUndeliveredItems();
        });
    }
    
    // إضافة مستمع حدث لزر التحديث
    const refreshBtn = document.getElementById('refreshUndeliveredBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', async () => {
            await loadCustodyItems();
            calculateAvailableQuantities();
            displayUndeliveredItems();
            loadAvailableCustodyForDelivery();
            console.log('تم تحديث العهد غير المسلمة');
        });
    }
}

// تحميل الأقسام
function loadDepartments() {
    const departmentFilter = document.getElementById('departmentFilter');
    if (!departmentFilter) return; // إذا لم يوجد العنصر لا تفعل شيئًا
    const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))];
    departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';
    departments.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept;
        option.textContent = dept;
        departmentFilter.appendChild(option);
    });
}

// إعداد مستمعي أحداث التقارير
function setupReportEventListeners() {
    // تقرير العهد حسب الموظف
    document.getElementById('generateEmployeeReportBtn').addEventListener('click', generateEmployeeReport);
    document.getElementById('exportEmployeeReportBtn').addEventListener('click', () => exportToExcel('employee-report'));
    
    // تقرير العهد المسترجعة
    document.getElementById('generateReturnedReportBtn').addEventListener('click', generateReturnedReport);
    document.getElementById('exportReturnedReportBtn').addEventListener('click', () => exportToExcel('returned-report'));
    
    // تقرير العهد غير المسترجعة
    document.getElementById('generatePendingReportBtn').addEventListener('click', generatePendingReport);
    document.getElementById('exportPendingReportBtn').addEventListener('click', () => exportToExcel('pending-report'));
    
    // تقرير حسب نوع العهدة
    document.getElementById('generateTypeReportBtn').addEventListener('click', generateTypeReport);
    document.getElementById('exportTypeReportBtn').addEventListener('click', () => exportToExcel('type-report'));
    
    // تقرير حسب القسم
    document.getElementById('generateDepartmentReportBtn').addEventListener('click', generateDepartmentReport);
    document.getElementById('exportDepartmentReportBtn').addEventListener('click', () => exportToExcel('department-report'));
}

// إنشاء تقرير العهد حسب الموظف
function generateEmployeeReport() {
    const searchTerm = document.getElementById('employeeReportSearch').value.toLowerCase();
    const tbody = document.getElementById('employeeReportBody');
    tbody.innerHTML = '';
    
    let filteredDeliveries = deliveryRecords;
    if (searchTerm) {
        filteredDeliveries = deliveryRecords.filter(record => 
            (record.employee_name && record.employee_name.toLowerCase().includes(searchTerm)) ||
            (record.employee_code && record.employee_code.toString().includes(searchTerm))
        );
    }
    
    // تجميع العهد حسب الموظف
    const employeeDeliveries = {};
    
    filteredDeliveries.forEach(record => {
        if (!employeeDeliveries[record.employee_code]) {
            employeeDeliveries[record.employee_code] = {
                name: record.employee_name,
                department: record.department || 'غير محدد',
                deliveries: []
            };
        }
        employeeDeliveries[record.employee_code].deliveries.push(record);
    });
    
    // عرض العهد لكل موظف
    Object.keys(employeeDeliveries).forEach(empCode => {
        const employee = employeeDeliveries[empCode];
        
        employee.deliveries.forEach(record => {
            const status = 'مسلمة';
            const returnDate = '-';
            const custodyItem = custodyItems.find(item => item.custody_code === record.custody_code);
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${record.employee_code}</td>
                <td>${record.employee_name}</td>
                <td>${employee.department}</td>
                <td>${custodyItem ? custodyItem.name : record.custody_type || 'غير محدد'}</td>
                <td>${record.quantity}</td>
                <td>${new Date(record.delivery_date).toLocaleDateString('ar-EG')}</td>
                <td><span class="status-${status === 'مسترجعة' ? 'returned' : 'delivered'}">${status}</span></td>
                <td>${returnDate}</td>
            `;
            tbody.appendChild(row);
        });
    });
    
    // إظهار رسالة إذا لم يتم العثور على نتائج
    if (tbody.children.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="no-data">لا توجد بيانات متاحة</td></tr>';
    }
}



// إنشاء تقرير العهد المسلمة
function generatePendingReport() {
    const tbody = document.getElementById('pendingReportBody');
    tbody.innerHTML = '';
    
    // الحصول على جميع العهد المسلمة (بما أنه لا يوجد استرجاع)
    const pendingDeliveries = deliveryRecords.filter(record => record.status === 'مسلم');
    
    // تجميع البيانات حسب الموظف والعهدة
    pendingDeliveries.forEach(record => {
        // البحث عن معلومات العهدة
        const custodyItem = custodyItems.find(item => item.custody_code === record.custody_code);
        
        // حساب المدة منذ التسليم
        const deliveryDate = new Date(record.delivery_date);
        const today = new Date();
        const daysDiff = Math.floor((today - deliveryDate) / (1000 * 60 * 60 * 24));
        
        // الكمية المتبقية هي نفس الكمية المسلمة (بما أنه لا يوجد استرجاع)
        const remainingQuantity = record.quantity;
        
        // البحث عن معلومات القسم
        const employee = employees.find(emp => emp.code === record.employee_code);
        const department = employee ? employee.department : 'غير محدد';
        
        // تحديد حالة العهدة بناءً على المدة
        let statusClass = 'normal';
        let statusText = 'عادية';
        
        if (daysDiff > 180) { // أكثر من 6 أشهر
            statusClass = 'critical';
            statusText = 'متأخرة جداً';
        } else if (daysDiff > 90) { // أكثر من 3 أشهر
            statusClass = 'warning';
            statusText = 'متأخرة';
        }
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${record.employee_code}</td>
            <td>${record.employee_name}</td>
            <td>${department}</td>
            <td>${custodyItem ? custodyItem.name : record.custody_type || 'غير محدد'}</td>
            <td>${record.quantity}</td>
            <td>${returnedQuantity}</td>
            <td>${remainingQuantity}</td>
            <td>${deliveryDate.toLocaleDateString('ar-EG')}</td>
            <td>${daysDiff} يوم</td>
            <td><span class="status-${statusClass}">${statusText}</span></td>
        `;
        tbody.appendChild(row);
    });
    
    // إظهار رسالة إذا لم يتم العثور على نتائج
    if (tbody.children.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="no-data">لا توجد عهد معلقة</td></tr>';
    }
}

// إنشاء تقرير حسب نوع العهدة
function generateTypeReport() {
    const selectedType = document.getElementById('custodyTypeFilter').value;
    const tbody = document.getElementById('typeReportBody');
    tbody.innerHTML = '';
    
    // الحصول على أنواع العهد المتاحة
    let custodyTypes = [...new Set(custodyItems.map(item => item.type).filter(Boolean))];
    if (selectedType) {
        custodyTypes = [selectedType];
    }
    
    // تجميع البيانات حسب نوع العهدة
    custodyTypes.forEach(type => {
        // الحصول على العهد من هذا النوع
        const typeItems = custodyItems.filter(item => item.type === type);
        
        // حساب إجمالي الكميات المتاحة
        const totalQuantity = typeItems.reduce((sum, item) => sum + item.quantity, 0);
        const availableQuantity = typeItems.reduce((sum, item) => sum + (item.available_quantity || 0), 0);
        
        // الحصول على سجلات التسليم لهذا النوع
        const typeDeliveries = deliveryRecords.filter(record => {
            const custodyItem = custodyItems.find(item => item.custody_code === record.custody_code);
            return custodyItem && custodyItem.type === type;
        });
        
        // بعد حذف قسم الاسترجاع، لا توجد كميات مسترجعة
        const totalDelivered = typeDeliveries.reduce((sum, record) => sum + record.quantity, 0);
        const totalReturned = 0;
        const damagedItems = 0;
        const lostItems = 0;

        // جميع الكميات المسلمة تبقى مع الموظفين
        const remainingWithEmployees = totalDelivered;
        
        // الحصول على قائمة الموظفين الذين لديهم عهد من هذا النوع
        // بعد حذف قسم الاسترجاع، جميع العهد المسلمة تبقى مع الموظفين
        const employeesWithCustody = typeDeliveries
            .filter(delivery => delivery.status === 'مسلم')
            .map(delivery => delivery.employee_name);
        
        const uniqueEmployees = [...new Set(employeesWithCustody)];
        const employeesList = uniqueEmployees.join(', ');
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${type}</td>
            <td>${totalQuantity}</td>
            <td>${availableQuantity}</td>
            <td>${remainingWithEmployees}</td>
            <td>${damagedItems}</td>
            <td>${lostItems}</td>
            <td>${uniqueEmployees.length}</td>
            <td>${employeesList || '-'}</td>
        `;
        tbody.appendChild(row);
    });
    
    // إظهار رسالة إذا لم يتم العثور على نتائج
    if (tbody.children.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="no-data">لا توجد بيانات متاحة</td></tr>';
    }
}

// إنشاء تقرير حسب القسم
function generateDepartmentReport() {
    const selectedDepartment = document.getElementById('departmentFilter').value;
    const tbody = document.getElementById('departmentReportBody');
    tbody.innerHTML = '';
    
    // الحصول على قائمة الأقسام
    const departments = [...new Set(employees.map(emp => emp.department).filter(Boolean))];
    const departmentsToProcess = selectedDepartment ? [selectedDepartment] : departments;
    
    // تجميع البيانات حسب القسم
    departmentsToProcess.forEach(department => {
        // الحصول على موظفي القسم
        const departmentEmployees = employees.filter(emp => emp.department === department);
        const employeeCodes = departmentEmployees.map(emp => emp.code);
        
        // الحصول على سجلات التسليم لموظفي القسم
        const departmentDeliveries = deliveryRecords.filter(record => 
            employeeCodes.includes(record.employee_code)
        );
        
        // تجميع البيانات حسب نوع العهدة
        const custodyTypeMap = {};
        
        departmentDeliveries.forEach(delivery => {
            const custodyItem = custodyItems.find(item => item.custody_code === delivery.custody_code);
            const custodyType = custodyItem ? custodyItem.type : (delivery.custody_type || 'غير محدد');
            
            if (!custodyTypeMap[custodyType]) {
                custodyTypeMap[custodyType] = {
                    delivered: 0,
                    returned: 0,
                    damaged: 0,
                    lost: 0,
                    employees: new Set()
                };
            }
            
            // إضافة الكمية المسلمة
            custodyTypeMap[custodyType].delivered += delivery.quantity;
            custodyTypeMap[custodyType].employees.add(delivery.employee_name);
            
            // بعد حذف قسم الاسترجاع، لا توجد كميات مسترجعة أو معطوبة أو مفقودة
        });
        
        // عرض البيانات لكل نوع عهدة في القسم
        Object.entries(custodyTypeMap).forEach(([type, data]) => {
            const remaining = data.delivered - data.returned;
            const employeesList = [...data.employees].join(', ');
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${department}</td>
                <td>${type}</td>
                <td>${data.delivered}</td>
                <td>${data.returned}</td>
                <td>${data.damaged}</td>
                <td>${data.lost}</td>
                <td>${data.employees.size}</td>
                <td>${employeesList}</td>
            `;
            tbody.appendChild(row);
        });
    });
    
    // إظهار رسالة إذا لم يتم العثور على نتائج
    if (tbody.children.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="no-data">لا توجد بيانات متاحة</td></tr>';
    }
}

// تصدير إلى Excel
function exportToExcel(type) {
    let data = [];
    let headers = [];
    let fileName = '';
    let tableElement = null;
    
    // تحديد البيانات والعناوين حسب نوع التقرير
    switch (type) {
        case 'custody':
            headers = ['كود العهدة', 'اسم العهدة', 'نوع العهدة', 'الحالة', 'الكمية الكلية', 'الكمية المتاحة', 'تاريخ الإضافة', 'الإجراءات'];
            fileName = 'قائمة_العهد';
            
            // استخراج البيانات من جدول العهد
            tableElement = document.querySelector('.custody-table');
            if (tableElement) {
                const rows = tableElement.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    // تجاهل عمود الإجراءات
                    const cells = row.querySelectorAll('td:not(:last-child)');
                    if (cells.length > 0) {
                        data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                    }
                });
                // تحديث العناوين لتجاهل عمود الإجراءات
                headers.pop();
            }
            break;
            
        case 'undelivered':
            headers = ['كود العهدة', 'اسم العهدة', 'نوع العهدة', 'الكمية المتاحة', 'تاريخ الإضافة'];
            fileName = 'العهد_غير_المسلمة';
            
            // استخراج البيانات من جدول العهد غير المسلمة
            tableElement = document.querySelector('.undelivered-table');
            if (tableElement) {
                const rows = tableElement.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length > 0) {
                        data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                    }
                });
            }
            break;
            
        case 'employee-report':
            headers = ['اسم الموظف', 'القسم', 'اسم العهدة', 'الكمية', 'تاريخ التسليم', 'الحالة'];
            fileName = 'تقرير_العهد_حسب_الموظف';
            
            // استخراج البيانات من جدول التقرير
            const employeeRows = document.querySelectorAll('#employeeReportBody tr');
            employeeRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                }
            });
            break;
            

            
        case 'pending-report':
            headers = ['اسم الموظف', 'القسم', 'اسم العهدة', 'الكمية الكلية', 'الكمية المسترجعة', 'الكمية المتبقية', 'تاريخ التسليم', 'المدة', 'الحالة'];
            fileName = 'تقرير_العهد_المعلقة';
            
            const pendingRows = document.querySelectorAll('#pendingReportBody tr');
            pendingRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                }
            });
            break;
            
        case 'type-report':
            headers = ['نوع العهدة', 'إجمالي الكمية', 'الكمية المتاحة', 'الكمية المسلمة', 'الكمية المسترجعة', 'الكمية المتبقية', 'المعطوب', 'المفقود', 'عدد الموظفين', 'قائمة الموظفين'];
            fileName = 'تقرير_العهد_حسب_النوع';
            
            const typeRows = document.querySelectorAll('#typeReportBody tr');
            typeRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                }
            });
            break;
            
        case 'department-report':
            headers = ['القسم', 'نوع العهدة', 'الكمية المسلمة', 'الكمية المسترجعة', 'الكمية المتبقية', 'المعطوب', 'المفقود', 'عدد الموظفين', 'قائمة الموظفين'];
            fileName = 'تقرير_العهد_حسب_القسم';
            
            const departmentRows = document.querySelectorAll('#departmentReportBody tr');
            departmentRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                }
            });
            break;
            
        case 'employee-custody':
            headers = ['كود العهدة', 'اسم العهدة', 'نوع العهدة', 'تاريخ الاستلام', 'الكمية المستلمة'];
            fileName = 'عهدة_موظف';
            
            // استخراج البيانات من جدول عهدة الموظف
            tableElement = document.querySelector('.report-table');
            if (tableElement) {
                const rows = tableElement.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length > 0) {
                        data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                    }
                });
                
                // إضافة اسم الموظف للملف إذا كان متاحًا
                try {
                    const employeeNameElement = document.getElementById('reportEmployeeName');
                    if (employeeNameElement && employeeNameElement.textContent) {
                        fileName = 'عهدة_' + employeeNameElement.textContent.trim();
                    }
                } catch (error) {
                    console.error('خطأ في الحصول على اسم الموظف:', error);
                    // استخدام الاسم الافتراضي إذا حدث خطأ
                }
            }
            break;
            
        case 'delivery':
            headers = ['رقم العملية', 'كود العهدة', 'اسم العهدة', 'نوع العهدة', 'اسم الموظف', 'القسم', 'الكمية', 'تاريخ التسليم'];
            fileName = 'سجل_تسليم_العهد';
            
            // استخراج البيانات من جدول سجل التسليم
            tableElement = document.querySelector('.delivery-table');
            if (tableElement) {
                const rows = tableElement.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length > 0) {
                        data.push(Array.from(cells).map(cell => cell.textContent.trim()));
                    }
                });
            }
            break;

            
        default:
            alert('نوع التقرير غير معروف');
            return;
    }
    
    // التحقق من وجود بيانات
    if (data.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    try {
        // إضافة BOM للتأكد من دعم اللغة العربية
        let csvContent = "\uFEFF";
        
        // إضافة العناوين
        csvContent += headers.map(header => `"${header}"`).join(';') + '\r\n';
        
        // إضافة البيانات
        data.forEach(row => {
            const processedRow = row.map(cell => {
                if (cell !== null && cell !== undefined) {
                    const cellStr = String(cell);
                    return '"' + cellStr.replace(/"/g, '""') + '"';
                }
                return '';
            });
            csvContent += processedRow.join(';') + '\r\n';
        });
        
        // تنزيل الملف
        const encodedUri = encodeURI("data:text/csv;charset=utf-8," + csvContent);
        const link = document.createElement('a');
        link.setAttribute('href', encodedUri);
        link.setAttribute('download', `${fileName}_${new Date().toISOString().split('T')[0]}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        alert('تم تصدير التقرير بنجاح');
    } catch (error) {
        console.error('خطأ في تصدير التقرير:', error);
        alert('حدث خطأ أثناء تصدير التقرير');
    }
}

// فحص الصلاحيات
function hasPermission(permission) {
  try {
    const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
    const result = permissions[permission] === true;
    console.log(`[Custody] hasPermission(${permission}) = ${result}`, permissions);
    return result;
  } catch (error) {
    console.error('خطأ في قراءة الصلاحيات:', error);
    return false;
  }
}

// تعديل عهدة
function editCustody(id) {
    // فحص صلاحية التعديل
    if (!hasPermission('edit_custody')) {
        alert('ليس لديك صلاحية لتعديل العهد');
        return;
    }

    const custody = custodyItems.find(item => item.id === id);
    if (custody) {
        document.getElementById('editCustodyId').value = custody.id;

        // استخدام الأسماء الصحيحة للحقول
        const custodyName = custody.custody_name || custody.name || '';
        const custodyType = custody.custody_type || custody.type || '';
        const custodyStatus = custody.status || 'جديدة';
        const custodyQuantity = custody.quantity || 1;

        document.getElementById('editCustodyName').value = custodyName;
        document.getElementById('editCustodyStatus').value = custodyStatus;
        document.getElementById('editCustodyQuantity').value = custodyQuantity;

        // تحديث خيارات نوع العهدة ديناميكياً
        updateCustodyTypeOptions(custodyType);

        console.log('تم تحميل بيانات العهدة للتعديل:', {
            id: custody.id,
            name: custodyName,
            type: custodyType,
            status: custodyStatus,
            quantity: custodyQuantity
        });

        document.getElementById('editCustodyModal').style.display = 'block';
    }
}

// تحديث خيارات نوع العهدة ديناميكياً
function updateCustodyTypeOptions(currentType) {
    const typeSelect = document.getElementById('editCustodyType');
    if (!typeSelect) return;

    // الحصول على جميع أنواع العهدة الموجودة في البيانات
    const existingTypes = [...new Set(custodyItems.map(item => {
        return item.custody_type || item.type || '';
    }).filter(type => type.trim() !== ''))];

    // الأنواع الافتراضية
    const defaultTypes = ['لابتوب', 'موبايل', 'شاشة', 'طابعة', 'كيبورد', 'ماوس', 'الكترونيات', 'أثاث', 'أخرى'];

    // دمج الأنواع الموجودة مع الافتراضية
    const allTypes = [...new Set([...defaultTypes, ...existingTypes])];

    // مسح الخيارات الحالية
    typeSelect.innerHTML = '';

    // إضافة الخيارات
    allTypes.forEach(type => {
        const option = document.createElement('option');
        option.value = type;
        option.textContent = type;
        if (type === currentType) {
            option.selected = true;
        }
        typeSelect.appendChild(option);
    });

    // إذا لم يتم العثور على النوع الحالي، أضفه
    if (currentType && !allTypes.includes(currentType)) {
        const option = document.createElement('option');
        option.value = currentType;
        option.textContent = currentType;
        option.selected = true;
        typeSelect.appendChild(option);
    }

    console.log('تم تحديث خيارات نوع العهدة:', allTypes);
    console.log('النوع المحدد:', currentType);
}

// حذف عهدة
async function deleteCustody(id) {
    // فحص صلاحية الحذف
    if (!hasPermission('delete_custody')) {
        alert('ليس لديك صلاحية لحذف العهد');
        return;
    }

    if (confirm('هل أنت متأكد من حذف هذه العهدة؟')) {
        try {
            const token = localStorage.getItem('token');
            const response = await fetch(`${API_URL}/custody/${id}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (response.ok) {
                alert('تم حذف العهدة بنجاح');
                await loadCustodyItems();
                calculateAvailableQuantities();
                displayCustodyItems();
                displayUndeliveredItems();
                loadAvailableCustodyForDelivery();
            } else {
                const error = await response.json();
                alert(error.error || 'خطأ في حذف العهدة');
            }
        } catch (error) {
            console.error('خطأ في حذف العهدة:', error);
            alert('خطأ في الاتصال بالخادم');
        }
    }
}

// إعداد مستمعي أحداث النافذة المنبثقة
function setupModalEventListeners() {
    // نافذة تعديل العهدة
    const custodyModal = document.getElementById('editCustodyModal');
    const custodyCloseBtn = custodyModal.querySelector('.close');
    const custodyCancelBtn = custodyModal.querySelector('.cancel-btn');
    const custodySaveBtn = document.getElementById('saveEditBtn');

    custodyCloseBtn.addEventListener('click', () => {
        custodyModal.style.display = 'none';
    });

    custodyCancelBtn.addEventListener('click', () => {
        custodyModal.style.display = 'none';
    });

    custodySaveBtn.addEventListener('click', saveEditedCustody);

    // نافذة تعديل سجل التسليم
    const deliveryModal = document.getElementById('editDeliveryModal');
    const deliveryCloseBtn = deliveryModal.querySelector('.close');
    const deliveryCancelBtn = deliveryModal.querySelector('.cancel-btn');
    const deliverySaveBtn = document.getElementById('saveEditDeliveryBtn');

    deliveryCloseBtn.addEventListener('click', () => {
        deliveryModal.style.display = 'none';
    });

    deliveryCancelBtn.addEventListener('click', () => {
        deliveryModal.style.display = 'none';
    });

    if (deliverySaveBtn) {
        console.log('✅ تم العثور على زر حفظ التسليم وإضافة event listener');
        deliverySaveBtn.addEventListener('click', saveEditedDelivery);
    } else {
        console.error('❌ لم يتم العثور على زر حفظ التسليم');
    }



    // تم تعطيل مراجع الإرجاع مؤقتاً
    // returnCloseBtn.addEventListener('click', () => {
    //     returnModal.style.display = 'none';
    // });

    // returnCancelBtn.addEventListener('click', () => {
    //     returnModal.style.display = 'none';
    // });

    // returnSaveBtn.addEventListener('click', saveEditedReturn);

    // إغلاق النوافذ عند النقر خارجها
    window.addEventListener('click', (event) => {
        if (event.target === custodyModal) {
            custodyModal.style.display = 'none';
        }
        if (event.target === deliveryModal) {
            deliveryModal.style.display = 'none';
        }

    });
}

// حفظ تعديل العهدة
async function saveEditedCustody() {
    const id = document.getElementById('editCustodyId').value;
    const formData = {
        name: document.getElementById('editCustodyName').value,
        type: document.getElementById('editCustodyType').value,
        status: document.getElementById('editCustodyStatus').value,
        quantity: parseInt(document.getElementById('editCustodyQuantity').value)
    };
    
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/custody/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(formData)
        });
        
        if (response.ok) {
            alert('تم تحديث العهدة بنجاح');
            document.getElementById('editCustodyModal').style.display = 'none';
            await loadCustodyItems();
            calculateAvailableQuantities();
            displayCustodyItems();
            displayUndeliveredItems();
            loadAvailableCustodyForDelivery();
        } else {
            alert('خطأ في تحديث العهدة');
        }
    } catch (error) {
        console.error('خطأ في تحديث العهدة:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// استخدام دالة showNotification من shared-utils.js

// دوال تعديل وحذف سجلات التسليم
function editDelivery(deliveryId) {
    // فحص صلاحية التعديل
    if (!hasPermission('edit_deliver_custody')) {
        alert('ليس لديك صلاحية لتعديل سجلات التسليم');
        return;
    }

    const delivery = deliveryRecords.find(record => record.id === deliveryId);
    if (!delivery) {
        alert('سجل التسليم غير موجود');
        return;
    }

    // ملء النافذة المنبثقة بالبيانات الحالية
    document.getElementById('editDeliveryId').value = delivery.id;
    document.getElementById('editDeliveryOperationNumber').value = delivery.operation_number;
    document.getElementById('editDeliveryEmployeeCode').value = delivery.employee_code;
    document.getElementById('editDeliveryEmployeeName').value = delivery.employee_name;
    document.getElementById('editDeliveryDepartment').value = delivery.department || '';
    document.getElementById('editDeliveryCustodyCode').value = delivery.custody_code;
    document.getElementById('editDeliveryCustodyName').value = delivery.custody_name || getCustodyName(delivery.custody_code);
    document.getElementById('editDeliveryCustodyType').value = delivery.custody_type;
    document.getElementById('editDeliveryQuantity').value = delivery.quantity;

    // تنسيق التاريخ للإدخال
    const deliveryDate = delivery.delivery_date;
    if (deliveryDate) {
        if (typeof DateUtils !== 'undefined') {
            document.getElementById('editDeliveryDate').value = DateUtils.formatDateForInput(deliveryDate);
        } else {
            // Fallback: تحويل التاريخ إلى صيغة YYYY-MM-DD
            const date = new Date(deliveryDate);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            document.getElementById('editDeliveryDate').value = `${year}-${month}-${day}`;
        }
    }

    document.getElementById('editDeliveryStatus').value = delivery.status;

    // فتح النافذة المنبثقة
    document.getElementById('editDeliveryModal').style.display = 'block';

    console.log('تم تحميل بيانات التسليم للتعديل');
}

function deleteDelivery(deliveryId) {
    // فحص صلاحية الحذف
    if (!hasPermission('delete_deliver_custody')) {
        alert('ليس لديك صلاحية لحذف سجلات التسليم');
        return;
    }

    if (!confirm('هل أنت متأكد من حذف سجل التسليم؟')) {
        return;
    }
    
    const token = localStorage.getItem('token');
    fetch(`${API_URL}/custody/delivery/${deliveryId}`, {
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`
        }
    })
    .then(response => {
        if (response.ok) {
            alert('تم حذف سجل التسليم بنجاح');
            loadDeliveryRecords();
            loadCustodyItems().then(() => {
                calculateAvailableQuantities();
                loadAvailableCustodyForDelivery();
            });
            displayDeliveryRecords();
        } else {
            throw new Error('فشل في حذف سجل التسليم');
        }
    })
    .catch(error => {
        console.error('خطأ في حذف سجل التسليم:', error);
        alert('خطأ في حذف سجل التسليم');
    });
}



// حفظ تعديلات سجل التسليم
async function saveEditedDelivery() {
    console.log('🔧 بدء حفظ تعديلات سجل التسليم');
    const deliveryId = document.getElementById('editDeliveryId').value;
    console.log('معرف التسليم:', deliveryId);
    const formData = {
        employee_code: document.getElementById('editDeliveryEmployeeCode').value,
        employee_name: document.getElementById('editDeliveryEmployeeName').value,
        department: document.getElementById('editDeliveryDepartment').value,
        custody_code: document.getElementById('editDeliveryCustodyCode').value,
        custody_name: document.getElementById('editDeliveryCustodyName').value,
        custody_type: document.getElementById('editDeliveryCustodyType').value,
        quantity: parseInt(document.getElementById('editDeliveryQuantity').value),
        delivery_date: document.getElementById('editDeliveryDate').value,
        status: document.getElementById('editDeliveryStatus').value
    };

    // التحقق من صحة البيانات
    if (!formData.employee_code || !formData.employee_name || !formData.custody_code ||
        !formData.delivery_date || !formData.quantity) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_URL}/custody/delivery/${deliveryId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(formData)
        });

        if (response.ok) {
            alert('تم تحديث سجل التسليم بنجاح');
            document.getElementById('editDeliveryModal').style.display = 'none';
            await loadDeliveryRecords();
            displayDeliveryRecords();
        } else {
            const error = await response.json();
            alert(error.error || 'خطأ في تحديث سجل التسليم');
        }
    } catch (error) {
        console.error('خطأ في تحديث سجل التسليم:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}





// تعيين التاريخ الحالي
function setCurrentDate() {
    const today = new Date().toISOString().split('T')[0];
    const deliveryDateInput = document.getElementById('deliveryDate');

    if (deliveryDateInput) {
        deliveryDateInput.value = today;
    }
}

// عرض تفاصيل مستلمي العهدة
function showCustodyDetails(custodyId) {
    const custody = custodyItems.find(item => item.id === custodyId);
    if (!custody) {
        alert('لم يتم العثور على العهدة');
        return;
    }

    // البحث عن جميع سجلات التسليم لهذه العهدة
    const custodyDeliveries = deliveryRecords.filter(record => 
        record.custody_code === custody.custody_code
    );

    if (custodyDeliveries.length === 0) {
        alert(`العهدة: ${custody.name} لم يتم تسليمها لأي موظف بعد`);
        return;
    }

    // عرض النافذة المنبثقة
    const modal = document.getElementById('custodyDetailsModal');
    modal.style.display = 'block';
    
    // إغلاق النافذة عند النقر على زر الإغلاق
    const closeButtons = modal.querySelectorAll('.close, .close-btn');
    closeButtons.forEach(button => {
        button.onclick = function() {
            modal.style.display = 'none';
        };
    });
    
    // إغلاق النافذة عند النقر خارجها (محسن لتجنب التعارض مع القائمة الجانبية)
    modal.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
    
    // ملء بيانات العهدة
    document.getElementById('detailsCustodyCode').textContent = custody.custody_code;
    document.getElementById('detailsCustodyName').textContent = custody.name;
    document.getElementById('detailsCustodyType').textContent = custody.type;
    
    // حساب الكميات
    const totalQuantity = custody.quantity;
    const deliveredQuantity = custodyDeliveries.reduce((sum, delivery) => sum + parseInt(delivery.quantity), 0);
    const availableQuantity = custody.available_quantity;
    
    document.getElementById('detailsTotalQuantity').textContent = totalQuantity;
    document.getElementById('detailsDeliveredQuantity').textContent = deliveredQuantity;
    document.getElementById('detailsAvailableQuantity').textContent = availableQuantity;
    
    // ملء جدول المستلمين
    const tableBody = document.getElementById('custodyRecipientsTableBody');
    tableBody.innerHTML = '';
    
    custodyDeliveries.forEach(delivery => {
        // البحث عن معلومات القسم للموظف
        const employee = employees.find(emp => emp.code === delivery.employee_code);
        const departmentName = employee ? employee.department : 'غير معروف';
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${delivery.employee_code}</td>
            <td>${delivery.employee_name}</td>
            <td>${departmentName}</td>
            <td>${delivery.quantity}</td>
            <td>${new Date(delivery.delivery_date).toLocaleDateString('ar-EG')}</td>
            <td><span class="status-${delivery.status === 'مسلم' ? 'delivered' : 'returned'}">${delivery.status}</span></td>
        `;
        tableBody.appendChild(row);
    });
    
    // إعداد زر الطباعة
    document.getElementById('printCustodyDetailsBtn').onclick = function() {
        printCustodyDetails(custody, custodyDeliveries);
    };
    
    // إعداد زر التصدير
    document.getElementById('exportCustodyDetailsBtn').onclick = function() {
        exportCustodyDetails(custody, custodyDeliveries);
    };
}

// طباعة تفاصيل العهدة
function printCustodyDetails(custody, deliveries) {
    const printWindow = window.open('', '', 'width=800,height=600');
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>تفاصيل مستلمي العهدة - ${custody.name}</title>
            <style>
                @page { size: A4; margin: 1cm; }
                body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                h1 { text-align: center; color: #333; margin-bottom: 20px; }
                h3 { margin-top: 20px; color: #555; border-bottom: 2px solid #2196F3; padding-bottom: 5px; }
                .info-container { display: flex; justify-content: space-between; margin-bottom: 20px; background-color: #f9f9f9; padding: 15px; border-radius: 5px; }
                .info-item { padding: 10px; }
                .info-label { font-weight: bold; color: #555; }
                table { width: 100%; border-collapse: collapse; margin-top: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
                th, td { border: 1px solid #ddd; padding: 10px; text-align: right; }
                th { background-color: #2196F3; color: white; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .summary { margin-top: 20px; display: flex; justify-content: space-around; }
                .summary-item { text-align: center; padding: 15px; border: 1px solid #ddd; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); background-color: #f2f2f2; width: 30%; }
                .summary-label { font-weight: bold; color: #333; }
                .summary-value { font-size: 1.2em; margin-top: 5px; color: #333; font-weight: bold; }
                .btn { display: inline-block; padding: 8px 20px; border-radius: 4px; cursor: pointer; font-weight: bold; text-align: center; margin: 0 10px; min-width: 100px; border: none; }
                .print-btn { background-color: #2196F3; color: white; }
                .close-btn { background-color: #f44336; color: white; }
                @media print { 
                    .no-print { display: none; } 
                    body { margin: 0; padding: 10px; }
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; page-break-after: auto; }
                    h1 { margin-top: 0; }
                }
            </style>
        </head>
        <body>
            <h1>تفاصيل مستلمي العهدة</h1>
            
            <div class="info-container">
                <div class="info-item"><span class="info-label">كود العهدة:</span> ${custody.custody_code}</div>
                <div class="info-item"><span class="info-label">اسم العهدة:</span> ${custody.name}</div>
                <div class="info-item"><span class="info-label">نوع العهدة:</span> ${custody.type}</div>
            </div>
            
            <div class="summary">
                <div class="summary-item">
                    <div class="summary-label">إجمالي العدد</div>
                    <div class="summary-value">${custody.quantity}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">المسلم</div>
                    <div class="summary-value">${deliveries.reduce((sum, delivery) => sum + parseInt(delivery.quantity), 0)}</div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">المتاح</div>
                    <div class="summary-value">${custody.available_quantity}</div>
                </div>
            </div>
            
            <h3>قائمة المستلمين</h3>
            <table>
                <thead>
                    <tr>
                        <th>كود الموظف</th>
                        <th>اسم الموظف</th>
                        <th>القسم</th>
                        <th>الكمية</th>
                        <th>تاريخ التسليم</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${deliveries.map(delivery => {
                        const employee = employees.find(emp => emp.code === delivery.employee_code);
                        const departmentName = employee ? employee.department : 'غير معروف';
                        return `
                            <tr>
                                <td>${delivery.employee_code}</td>
                                <td>${delivery.employee_name}</td>
                                <td>${departmentName}</td>
                                <td>${delivery.quantity}</td>
                                <td>${new Date(delivery.delivery_date).toLocaleDateString('ar-EG')}</td>
                                <td>${delivery.status}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
            
            <div class="no-print" style="margin-top: 30px; text-align: center;">
                <button onclick="window.print()" class="btn print-btn">طباعة</button>
                <button onclick="window.close()" class="btn close-btn">إغلاق</button>
            </div>
            
            <script>
                window.onload = function() {
                    setTimeout(function() {
                        window.print();
                    }, 500);
                }
            </script>
        </body>
        </html>
    `);
    printWindow.document.close();
}

// تصدير تفاصيل العهدة إلى ملف Excel
function exportCustodyDetails(custody, deliveries) {
    // تحضير البيانات للتصدير
    const headers = ['كود الموظف', 'اسم الموظف', 'القسم', 'الكمية', 'تاريخ التسليم', 'الحالة'];
    
    const data = deliveries.map(delivery => {
        const employee = employees.find(emp => emp.code === delivery.employee_code);
        const departmentName = employee ? employee.department : 'غير معروف';
        return [
            delivery.employee_code,
            delivery.employee_name,
            departmentName,
            delivery.quantity,
            new Date(delivery.delivery_date).toLocaleDateString('ar-EG'),
            delivery.status
        ];
    });
    
    // إنشاء مصفوفة تحتوي على معلومات العهدة
    const custodyInfo = [
        ['كود العهدة', custody.custody_code],
        ['اسم العهدة', custody.name],
        ['نوع العهدة', custody.type],
        ['إجمالي العدد', custody.quantity],
        ['المسلم', deliveries.reduce((sum, delivery) => sum + parseInt(delivery.quantity), 0)],
        ['المتاح', custody.available_quantity],
        [''] // صف فارغ
    ];
    
    // دمج البيانات
    const exportData = [
        ['تفاصيل مستلمي العهدة'],
        [''],
        ...custodyInfo,
        [''],
        ['قائمة المستلمين'],
        headers,
        ...data
    ];
    
    // إنشاء ملف Excel باستخدام SheetJS
    try {
        // إضافة BOM للتأكد من دعم اللغة العربية
        let csvContent = "\uFEFF";
        
        // استخدام الفاصلة المنقوطة بدلاً من الفاصلة العادية لتجنب مشاكل التنسيق
        exportData.forEach(row => {
            // معالجة كل قيمة لتجنب مشاكل الفواصل
            const processedRow = row.map(cell => {
                // إذا كانت القيمة نصية، نضعها بين علامات اقتباس
                if (cell !== null && cell !== undefined) {
                    const cellStr = String(cell);
                    // استبدال علامات الاقتباس المزدوجة بعلامتين متتاليتين لتجنب مشاكل التنسيق
                    return '"' + cellStr.replace(/"/g, '""') + '"';
                }
                return '';
            });
            csvContent += processedRow.join(';') + '\r\n';
        });
        
        // تنزيل الملف
        const encodedUri = encodeURI("data:text/csv;charset=utf-8," + csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `تفاصيل_مستلمي_العهدة_${custody.custody_code}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    } catch (e) {
        console.error('خطأ في تصدير تفاصيل العهدة:', e);
        alert('حدث خطأ أثناء تصدير التفاصيل');
    }
    console.log('تم تحميل ملف custody.js بنجاح');
}

// التحقق من المحتوى المحدد من البطاقات
function checkSelectedContent() {
  const selectedContent = localStorage.getItem('selectedCustodyTab');

  if (selectedContent) {
    // حذف المحتوى المحفوظ
    localStorage.removeItem('selectedCustodyTab');

    // عرض المحتوى المناسب
    showContent(selectedContent);
  } else {
    // عرض المحتوى الافتراضي (إضافة عهدة)
    showContent('add-custody');
  }
}

// عرض المحتوى المحدد
function showContent(contentType) {
  console.log('عرض المحتوى:', contentType);

  // إخفاء جميع المحتويات
  const allContents = document.querySelectorAll('.tab-content');
  allContents.forEach(content => {
    content.classList.remove('active');
    content.style.display = 'none';
  });

  // عرض المحتوى المحدد
  const targetContent = document.getElementById(contentType);
  if (targetContent) {
    targetContent.classList.add('active');
    targetContent.style.display = 'block';

    // تحديث عنوان الصفحة
    const pageTitle = document.querySelector('h1');
    if (pageTitle) {
      if (contentType === 'add-custody') {
        pageTitle.textContent = 'إضافة عهدة';
      } else if (contentType === 'deliver-custody') {
        pageTitle.textContent = 'تسليم العهد';

      } else if (contentType === 'undelivered-custody') {
        pageTitle.textContent = 'العهد غير المسلمة';
      } else if (contentType === 'employee-custody-report') {
        pageTitle.textContent = 'عهدة الموظف';
      }
    }

    // تحديث البيانات حسب نوع المحتوى
    if (contentType === 'undelivered-custody') {
      loadUndeliveredCustody();
    } else if (contentType === 'employee-custody-report') {
      // يمكن إضافة تحميل بيانات تقرير الموظف هنا إذا لزم الأمر
    }
  } else {
    console.error('لم يتم العثور على المحتوى:', contentType);
  }
}


