const express = require('express');
const router = express.Router();
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');
const { logAction, createEditMessage } = require('../activityLogger');

// دالة للتحقق من صحة التوكن
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'غير مصرح' });
  }

  const jwt = require('jsonwebtoken');
  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'توكن غير صالح' });
    }
    req.user = user;
    next();
  });
};

// الحصول على جميع العهد
router.get('/', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const [rows] = await pool.promise().query('SELECT * FROM custody ORDER BY created_at DESC, id DESC');
    res.json(rows);
  } catch (error) {
    console.error('Error fetching custody:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات العهد' });
  }
});

// إضافة عهدة جديدة
router.post('/', authenticateToken, async (req, res) => {
  const { custody_code, custody_name, custody_type, name, type, status, quantity, available_quantity, purchase_date, purchase_price, notes } = req.body;

  // دعم كلا من التنسيقين القديم والجديد
  const finalName = name || custody_name;
  const finalType = type || custody_type;

  if (!custody_code || !finalName || !finalType) {
    return res.status(400).json({ error: 'كود العهدة واسم العهدة ونوع العهدة مطلوبة' });
  }

  try {
    const pool = req.app.locals.pool;
    // التحقق من عدم وجود كود العهدة مسبقاً
    const [existingCode] = await pool.promise().query(
      'SELECT custody_code FROM custody WHERE custody_code = ?',
      [custody_code]
    );

    if (existingCode.length > 0) {
      return res.status(400).json({ error: 'كود العهدة موجود مسبقاً' });
    }

    // التحقق من عدم وجود اسم العهدة مسبقاً
    const [existingName] = await pool.promise().query(
      'SELECT name FROM custody WHERE name = ?',
      [finalName]
    );

    if (existingName.length > 0) {
      return res.status(409).json({
        error: 'اسم العهدة موجود مسبقاً. يرجى اختيار اسم مختلف.',
        duplicate_name: true,
        existing_name: finalName
      });
    }

    // إنشاء query ديناميكي بناءً على الحقول المتاحة (استخدام أسماء الأعمدة الصحيحة)
    let insertFields = ['custody_code', 'name', 'type'];
    let insertValues = [custody_code, finalName, finalType];
    let placeholders = ['?', '?', '?'];

    if (status) {
      insertFields.push('status');
      insertValues.push(status);
      placeholders.push('?');
    }

    if (quantity !== undefined) {
      insertFields.push('quantity');
      insertValues.push(quantity);
      placeholders.push('?');
    }

    if (available_quantity !== undefined) {
      insertFields.push('available_quantity');
      insertValues.push(available_quantity);
      placeholders.push('?');
    }

    if (purchase_date) {
      insertFields.push('purchase_date');
      insertValues.push(purchase_date);
      placeholders.push('?');
    }

    if (purchase_price !== undefined) {
      insertFields.push('purchase_price');
      insertValues.push(purchase_price);
      placeholders.push('?');
    }

    if (notes) {
      insertFields.push('notes');
      insertValues.push(notes);
      placeholders.push('?');
    }

    const [result] = await pool.promise().query(
      `INSERT INTO custody (${insertFields.join(', ')}) VALUES (${placeholders.join(', ')})`,
      insertValues
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'custody',
      record_id: result.insertId.toString(),
      message: `تم إضافة عهدة جديدة: ${finalName} - النوع: ${finalType} - الكمية: ${quantity || 1} - السعر: ${purchase_price || 'غير محدد'} جنيه`
    });

    res.status(201).json({
      message: 'تم إضافة العهدة بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('Error adding custody:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء إضافة العهدة' });
  }
});

// تحديث عهدة
router.put('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const { name, type, status, quantity, custody_name, custody_type, purchase_date, purchase_price, notes } = req.body;

  // دعم كلا من التنسيقين القديم والجديد
  const finalName = name || custody_name;
  const finalType = type || custody_type;

  if (!finalName || !finalType) {
    return res.status(400).json({ error: 'اسم العهدة ونوع العهدة مطلوبان' });
  }

  try {
    const pool = req.app.locals.pool;

    // الحصول على البيانات القديمة قبل التحديث
    const [oldDataRows] = await pool.promise().query(
      'SELECT * FROM custody WHERE id = ?',
      [id]
    );

    if (oldDataRows.length === 0) {
      return res.status(404).json({ error: 'العهدة غير موجودة' });
    }

    const oldData = oldDataRows[0];

    // إنشاء query ديناميكي بناءً على الحقول المتاحة
    let updateFields = [];
    let updateValues = [];

    if (finalName) {
      updateFields.push('name = ?');
      updateValues.push(finalName);
    }

    if (finalType) {
      updateFields.push('type = ?');
      updateValues.push(finalType);
    }

    if (quantity !== undefined) {
      updateFields.push('quantity = ?');
      updateValues.push(quantity);
    }

    if (status) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }

    if (purchase_date) {
      updateFields.push('purchase_date = ?');
      updateValues.push(purchase_date);
    }

    if (purchase_price !== undefined) {
      updateFields.push('purchase_price = ?');
      updateValues.push(purchase_price);
    }

    if (notes) {
      updateFields.push('notes = ?');
      updateValues.push(notes);
    }

    updateValues.push(id);

    const [result] = await pool.promise().query(
      `UPDATE custody SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'العهدة غير موجودة' });
    }

    // تسجيل النشاط
    const newData = { ...oldData };
    if (finalName) newData.name = finalName;
    if (finalType) newData.type = finalType;
    if (status) newData.status = status;
    if (quantity !== undefined) newData.quantity = quantity;
    if (purchase_date) newData.purchase_date = purchase_date;
    if (purchase_price !== undefined) newData.purchase_price = purchase_price;
    if (notes) newData.notes = notes;

    const fieldLabels = {
      name: 'اسم العهدة',
      type: 'نوع العهدة',
      status: 'الحالة',
      quantity: 'الكمية',
      purchase_date: 'تاريخ الشراء',
      purchase_price: 'سعر الشراء',
      notes: 'الملاحظات'
    };

    const editMessage = createEditMessage(
      `عهدة: ${newData.name}`,
      oldData,
      newData,
      fieldLabels
    );

    // تحديث سجل التسليم تلقائياً إذا تم تغيير اسم أو نوع العهدة
    if ((finalName && finalName !== oldData.name) || (finalType && finalType !== oldData.type)) {
      await pool.promise().query(
        `UPDATE custody_delivery
         SET custody_name = ?, custody_type = ?
         WHERE custody_code = ?`,
        [finalName || oldData.name, finalType || oldData.type, oldData.custody_code]
      );

      console.log(`تم تحديث سجل التسليم للعهدة ${oldData.custody_code} - الاسم الجديد: ${finalName || oldData.name}, النوع الجديد: ${finalType || oldData.type}`);
    }

    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'custody',
      record_id: id.toString(),
      message: editMessage
    });

    res.json({
      message: 'تم تحديث العهدة بنجاح',
      delivery_updated: (finalName && finalName !== oldData.name) || (finalType && finalType !== oldData.type)
    });
  } catch (error) {
    console.error('Error updating custody:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء تحديث العهدة' });
  }
});

// حذف عهدة
router.delete('/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    const pool = req.app.locals.pool;
    // أولاً، الحصول على بيانات العهدة كاملة من جدول custody
    const [custodyData] = await pool.promise().query(
      'SELECT * FROM custody WHERE id = ?',
      [id]
    );

    if (custodyData.length === 0) {
      return res.status(404).json({ error: 'العهدة غير موجودة' });
    }

    const custody = custodyData[0];
    const custodyCode = custody.custody_code;

    // التحقق من عدم وجود تسليمات مرتبطة بهذه العهدة
    const [deliveries] = await pool.promise().query(
      'SELECT id FROM custody_delivery WHERE custody_code = ?',
      [custodyCode]
    );

    if (deliveries.length > 0) {
      return res.status(400).json({
        error: `لا يمكن حذف هذه العهدة لأنها مسلمة لـ ${deliveries.length} موظف. يجب استرجاع العهدة أولاً قبل الحذف.`
      });
    }

    const [result] = await pool.promise().query('DELETE FROM custody WHERE id = ?', [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'العهدة غير موجودة' });
    }

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'custody',
      record_id: id.toString(),
      message: `تم حذف عهدة: ${custody.name} - النوع: ${custody.type} - الكود: ${custody.custody_code} - الكمية: ${custody.quantity || 1} - السعر: ${custody.purchase_price || 'غير محدد'} جنيه`
    });

    res.json({ message: 'تم حذف العهدة بنجاح' });
  } catch (error) {
    console.error('Error deleting custody:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      sql: error.sql,
      sqlMessage: error.sqlMessage
    });
    res.status(500).json({
      error: 'حدث خطأ أثناء حذف العهدة',
      details: error.message
    });
  }
});

// الحصول على جميع سجلات تسليم العهد
router.get('/delivery', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const [rows] = await pool.promise().query('SELECT * FROM custody_delivery WHERE status = "مسلم" ORDER BY created_at DESC, id DESC');
    res.json(rows);
  } catch (error) {
    console.error('Error fetching custody delivery:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب بيانات تسليم العهد' });
  }
});

// جلب أرقام العمليات لتوليد الرقم التالي
router.get('/delivery/operation-numbers', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const [rows] = await pool.promise().query('SELECT operation_number FROM custody_delivery ORDER BY operation_number DESC');
    res.json(rows);
  } catch (error) {
    console.error('Error fetching operation numbers:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب أرقام العمليات' });
  }
});

// تسليم عهدة لموظف
router.post('/delivery', authenticateToken, async (req, res) => {
  const {
    operation_number,
    custody_code,
    custody_name,
    custody_type,
    employee_code,
    employee_name,
    department,
    quantity,
    delivery_date,
    status,
    notes
  } = req.body;

  if (!operation_number || !custody_code || !employee_code || !delivery_date || !quantity) {
    return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب ملؤها' });
  }

  // تحويل employee_code إلى رقم صحيح للتوافق مع قاعدة البيانات
  const employeeCodeInt = parseInt(employee_code);
  if (isNaN(employeeCodeInt)) {
    return res.status(400).json({ error: 'كود الموظف يجب أن يكون رقماً صحيحاً' });
  }

  try {
    const pool = req.app.locals.pool;
    // التحقق من عدم وجود رقم العملية مسبقاً
    const [existing] = await pool.promise().query(
      'SELECT operation_number FROM custody_delivery WHERE operation_number = ?',
      [operation_number]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: 'رقم العملية موجود مسبقاً' });
    }

    // التحقق من أن العهدة غير مسلمة حالياً (بناءً على كود العهدة)
    const [currentDelivery] = await pool.promise().query(
      'SELECT id FROM custody_delivery WHERE custody_code = ? AND status = "مسلم"',
      [custody_code]
    );

    if (currentDelivery.length > 0) {
      return res.status(400).json({ error: 'هذه العهدة مسلمة بالفعل لموظف آخر' });
    }

    const [result] = await pool.promise().query(
      `INSERT INTO custody_delivery
       (operation_number, custody_code, custody_name, custody_type, employee_code, employee_name, department, quantity, delivery_date, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [operation_number, custody_code, custody_name, custody_type, employeeCodeInt, employee_name, department, quantity, delivery_date, status || 'مسلم']
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'deliver',
      module: 'custody',
      record_id: result.insertId.toString(),
      message: `تم تسليم عهدة: ${custody_name} (كود: ${custody_code}) للموظف: ${employee_name} (كود: ${employee_code}) - القسم: ${department} - الكمية: ${quantity} - رقم العملية: ${operation_number}`
    });

    res.status(201).json({
      message: 'تم تسليم العهدة بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('Error delivering custody:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      sql: error.sql,
      sqlMessage: error.sqlMessage
    });
    res.status(500).json({
      error: 'حدث خطأ أثناء تسليم العهدة',
      details: error.message
    });
  }
});

// تعديل سجل تسليم العهد
router.put('/delivery/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const {
    employee_code,
    employee_name,
    department,
    custody_code,
    custody_name,
    custody_type,
    quantity,
    delivery_date,
    status
  } = req.body;

  // التحقق من البيانات المطلوبة
  if (!employee_code || !employee_name || !custody_code || !delivery_date || !quantity) {
    return res.status(400).json({ error: 'جميع البيانات المطلوبة يجب أن تكون موجودة' });
  }

  // تحويل employee_code إلى رقم صحيح للتوافق مع قاعدة البيانات
  const employeeCodeInt = parseInt(employee_code);
  if (isNaN(employeeCodeInt)) {
    return res.status(400).json({ error: 'كود الموظف يجب أن يكون رقماً صحيحاً' });
  }

  try {
    const pool = req.app.locals.pool;

    // الحصول على البيانات الأصلية قبل التحديث
    const [originalData] = await pool.promise().query(
      'SELECT * FROM custody_delivery WHERE id = ?',
      [id]
    );

    if (originalData.length === 0) {
      return res.status(404).json({ error: 'سجل التسليم غير موجود' });
    }

    const original = originalData[0];

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    const processedDeliveryDate = (delivery_date === '' || delivery_date === null || delivery_date === undefined) ? null : delivery_date;

    // تحديث سجل التسليم
    const [result] = await pool.promise().query(
      `UPDATE custody_delivery SET
        employee_code = ?,
        employee_name = ?,
        department = ?,
        custody_code = ?,
        custody_name = ?,
        custody_type = ?,
        quantity = ?,
        delivery_date = ?,
        status = ?
      WHERE id = ?`,
      [employeeCodeInt, employee_name, department, custody_code, custody_name, custody_type, quantity, processedDeliveryDate, status, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'سجل التسليم غير موجود' });
    }

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'custody_delivery',
      record_id: id.toString(),
      message: `تم تعديل سجل تسليم عهدة: ${custody_name} (كود: ${custody_code}) للموظف: ${employee_name} (كود: ${employeeCodeInt}) - القسم: ${department} - الكمية: ${quantity} - رقم العملية: ${original.operation_number}`
    });

    res.json({
      success: true,
      message: 'تم تحديث سجل التسليم بنجاح',
      id: parseInt(id)
    });
  } catch (error) {
    console.error('Error updating delivery record:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء تحديث سجل التسليم' });
  }
});

// استرجاع عهدة من موظف
router.put('/delivery/:id/return', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const { return_date, return_notes } = req.body;

  if (!return_date) {
    return res.status(400).json({ error: 'تاريخ الاسترجاع مطلوب' });
  }

  try {
    const pool = req.app.locals.pool;

    // الحصول على بيانات التسليم قبل التحديث
    const [deliveryData] = await pool.promise().query(
      'SELECT * FROM custody_delivery WHERE id = ?',
      [id]
    );

    if (deliveryData.length === 0) {
      return res.status(404).json({ error: 'سجل التسليم غير موجود' });
    }

    const delivery = deliveryData[0];

    // معالجة حقول التاريخ - تحويل القيم الفارغة إلى null
    const processedReturnDate = (return_date === '' || return_date === null || return_date === undefined) ? null : return_date;

    const [result] = await pool.promise().query(
      'UPDATE custody_delivery SET status = "مسترجع", return_date = ?, return_notes = ? WHERE id = ?',
      [processedReturnDate, return_notes, id]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'return',
      module: 'custody',
      record_id: id.toString(),
      message: `تم استرجاع عهدة: ${delivery.custody_name} (كود: ${delivery.custody_code}) من الموظف: ${delivery.employee_name} (كود: ${delivery.employee_code}) - تاريخ الاسترجاع: ${return_date} - رقم العملية: ${delivery.operation_number}`
    });

    res.json({ message: 'تم استرجاع العهدة بنجاح' });
  } catch (error) {
    console.error('Error returning custody:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء استرجاع العهدة' });
  }
});

// حذف سجل تسليم عهدة
router.delete('/delivery/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;

  try {
    const pool = req.app.locals.pool;

    // الحصول على بيانات السجل قبل الحذف
    const [deliveryData] = await pool.promise().query(
      'SELECT * FROM custody_delivery WHERE id = ?',
      [id]
    );

    if (deliveryData.length === 0) {
      return res.status(404).json({ error: 'سجل التسليم غير موجود' });
    }

    const delivery = deliveryData[0];

    // حذف السجل
    const [result] = await pool.promise().query('DELETE FROM custody_delivery WHERE id = ?', [id]);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'custody_delivery',
      record_id: id.toString(),
      message: `تم حذف سجل تسليم عهدة: ${delivery.custody_name} (كود: ${delivery.custody_code}) للموظف: ${delivery.employee_name} (كود: ${delivery.employee_code}) - القسم: ${delivery.department} - الكمية: ${delivery.quantity} - رقم العملية: ${delivery.operation_number}`
    });

    res.json({ message: 'تم حذف سجل التسليم بنجاح' });
  } catch (error) {
    console.error('Error deleting custody delivery:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء حذف سجل التسليم' });
  }
});

// الحصول على العهد المسلمة لموظف معين
router.get('/employee/:employeeCode', async (req, res) => {
  const { employeeCode } = req.params;

  try {
    const pool = req.app.locals.pool;
    const [rows] = await pool.promise().query(
      'SELECT * FROM custody_delivery WHERE employee_code = ? AND status = "مسلم" ORDER BY created_at DESC, id DESC',
      [employeeCode]
    );
    res.json(rows);
  } catch (error) {
    console.error('Error fetching employee custody:', error);
    res.status(500).json({ error: 'حدث خطأ أثناء جلب عهد الموظف' });
  }
});

// البحث في العهد
router.get('/search', async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    const {
      custody_code,
      name,
      type,
      status,
      min_price,
      max_price,
      start_date,
      end_date
    } = req.query;

    let query = `
      SELECT * FROM custody
      WHERE 1=1
    `;
    const params = [];

    // البحث بكود العهدة
    if (custody_code) {
      query += " AND custody_code = ?";
      params.push(custody_code);
    }

    // البحث باسم العهدة
    if (name) {
      query += " AND name LIKE ?";
      params.push(`%${name}%`);
    }

    // البحث بنوع العهدة
    if (type) {
      query += " AND type = ?";
      params.push(type);
    }

    // البحث بالحالة
    if (status) {
      query += " AND status = ?";
      params.push(status);
    }

    // البحث بنطاق السعر
    if (min_price) {
      query += " AND purchase_price >= ?";
      params.push(min_price);
    }

    if (max_price) {
      query += " AND purchase_price <= ?";
      params.push(max_price);
    }

    // البحث بنطاق التاريخ
    if (start_date) {
      query += " AND purchase_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND purchase_date <= ?";
      params.push(end_date);
    }

    query += " ORDER BY created_at DESC, id DESC";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في العهد:', error);
    res.status(500).json({ error: 'فشل في البحث في العهد' });
  }
});

// البحث في سجلات التسليم
router.get('/delivery/search', async (req, res) => {
  try {
    const pool = req.app.locals.pool;

    const {
      employee_code,
      employee_name,
      custody_type,
      start_date,
      end_date
    } = req.query;

    let query = `
      SELECT * FROM custody_delivery
      WHERE 1=1
    `;
    const params = [];

    // البحث بكود الموظف
    if (employee_code) {
      query += " AND employee_code = ?";
      params.push(employee_code);
    }

    // البحث باسم الموظف
    if (employee_name) {
      query += " AND employee_name LIKE ?";
      params.push(`%${employee_name}%`);
    }

    // البحث بنوع العهدة
    if (custody_type) {
      query += " AND custody_type = ?";
      params.push(custody_type);
    }

    // البحث بنطاق التاريخ
    if (start_date) {
      query += " AND delivery_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND delivery_date <= ?";
      params.push(end_date);
    }

    query += " ORDER BY delivery_date DESC, id DESC";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في سجلات التسليم:', error);
    res.status(500).json({ error: 'فشل في البحث في سجلات التسليم' });
  }
});

module.exports = router;
